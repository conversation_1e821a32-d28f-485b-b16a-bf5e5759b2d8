import React from "react";
import { useNavigate } from "react-router-dom";

interface UnderMaintenanceProps {
  isOverlay?: boolean;
  message?: string;
  expectedTime?: string;
  allowNavigation?: boolean;
  redirectPath?: string;
}

const UnderMaintenance: React.FC<UnderMaintenanceProps> = ({
  message = "We're currently under maintenance",
  expectedTime = "Please check back soon",
  allowNavigation = false,
  redirectPath = "/",
}) => {
  const navigate = useNavigate();

  React.useEffect(() => {
    const handlePopState = (e: PopStateEvent) => {
      if (!allowNavigation) {
        e.preventDefault();
        window.history.pushState(null, "", window.location.pathname);
      }
    };

    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (!allowNavigation) {
        e.preventDefault();
        e.returnValue = "";
        return "";
      }
    };

    // Prevent right-click context menu
    const handleContextMenu = (e: MouseEvent) => {
      if (!allowNavigation) {
        e.preventDefault();
      }
    };

    // Add all event listeners
    window.addEventListener("popstate", handlePopState);
    window.addEventListener("beforeunload", handleBeforeUnload);
    document.addEventListener("contextmenu", handleContextMenu);

    // Push a new history state to prevent going back
    if (!allowNavigation) {
      window.history.pushState(null, "", window.location.pathname);
    }

    // Clean up event listeners
    return () => {
      window.removeEventListener("popstate", handlePopState);
      window.removeEventListener("beforeunload", handleBeforeUnload);
      document.removeEventListener("contextmenu", handleContextMenu);
    };
  }, [allowNavigation]);

  // Force full page styles
  const containerStyles =
    "fixed inset-0 z-[10000] bg-white flex flex-col items-center justify-center bg-maintenance";

  return (
    <div className={containerStyles}>
      <div className="text-center p-6 max-w-lg">
        <img
          src="/images/frame1.png"
          alt="EagleFi Mascot"
          className="w-80 h-auto mx-auto "
        />

        <h1 className="text-3xl font-bold text-slate-700 mb-4">{message}</h1>

        <p className="text-gray-600 mb-6">{expectedTime}</p>

        {allowNavigation && (
          <button
            onClick={() => navigate(redirectPath)}
            className="bg-[#FDC500] border-2 border-[#000000] border-b-[4px] border-r-[3px] text-black font-bold px-6 py-2 rounded-xl hover:bg-[#e6b200] transition-colors"
          >
            Return Home
          </button>
        )}
      </div>
    </div>
  );
};

export default UnderMaintenance;
