import React from "react";
import Markdown from "react-markdown";
import useAutoScroll from "../hooks/useAutoScroll";
import Spinner from "../components/Spinner";
import userIcon from "/images/user.svg";
import errorIcon from "/images/error.svg";
import { Message } from "../types";
interface ChatMessagesProps {
  messages: Message[];
  isLoading: boolean;
}

const ChatMessages: React.FC<ChatMessagesProps> = ({ messages, isLoading }) => {
  const scrollContentRef = useAutoScroll({
    active: isLoading,
  }) as React.RefObject<HTMLDivElement>;

  return (
    <div ref={scrollContentRef} className="grow space-y-4">
      {messages.length > 0 && (
        <div className="bg-amber-50 border border-amber-200 rounded-md p-3 text-amber-800 text-sm">
          <span className="font-semibold">Beta Version Notice:</span> EagleAI is
          currently in beta. You may encounter some issues while using it. We
          appreciate your patience and feedback as we improve the service.
        </div>
      )}
      {messages.map(({ role, content, loading, error }, idx) => (
        <div
          key={idx}
          className={`flex items-start gap-4 py-4 px-3 rounded-xl ${
            role === "user" ? "bg-black/10" : ""
          }`}
        >
          {role === "user" && (
            <img
              className="h-[26px] w-[26px] shrink-0"
              src={userIcon}
              alt="user"
            />
          )}
          <div>
            <div className="markdown-container">
              {loading && !content ? (
                <Spinner />
              ) : role === "model" ? (
                <div className="flex">
                  <img
                    className="h-[30px] w-auto mr-2"
                    src="/images/logo header.svg"
                    alt="user"
                  />
                  <div>
                    <Markdown>{content}</Markdown>
                  </div>
                </div>
              ) : (
                <div className="whitespace-pre-line">{content}</div>
              )}
            </div>
            {error && (
              <div
                className={`flex items-center gap-1 text-sm text-error-red ${
                  content && "mt-2"
                }`}
              >
                <img className="h-5 w-5" src={errorIcon} alt="error" />
                <span>Error generating the response</span>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ChatMessages;
