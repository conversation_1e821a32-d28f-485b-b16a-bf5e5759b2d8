import { useEffect } from "react";

const BuyCrypto = () => {
  useEffect(() => {
    // Load the Let's Exchange widget script
    const script = document.createElement("script");
    script.src = "https://letsexchange.io/init_widget.js";
    script.async = true;
    document.body.appendChild(script);

    // Add stylesheet
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.type = "text/css";
    link.href = "https://letsexchange.io/widget_lets.css";
    document.head.appendChild(link);

    // Clean up on component unmount
    return () => {
      document.body.removeChild(script);
      document.head.removeChild(link);
    };
  }, []);

  return (
    <div className="main-content-wrap flex flex-col justify-between min-h-screen">
      <div className="main-content">
        <div className="container max-sm:px-0">
          <div className="flex flex-col items-center mt-1 mb-8">
            {/* Exchange Widget */}
            <div className="mt-8 w-full max-w-lg">
              <h2 className="text-2xl font-bold text-slate-700 dark:text-white text-center mb-4">
                Bridge
              </h2>
              <div
                className="lets-widget"
                id="lets_widget_hwn4AYoQ3WlgA0A4"
                style={{
                  maxWidth: "480px",
                  height: "520px", // Increased height to eliminate scrollbar
                  margin: "0 auto",
                  overflow: "hidden", // Prevent scrollbars
                }}
              >
                <iframe
                  src="https://letsexchange.io/v2/widget?affiliate_id=hwn4AYoQ3WlgA0A4&is_iframe=true"
                  width="100%"
                  height="100%"
                  frameBorder="0"
                  scrolling="no" // Disable scrolling
                  allow="clipboard-read; clipboard-write"
                  title="Let's Exchange Widget"
                ></iframe>
              </div>
            </div>

            {/* Button Widget */}
            {/* <div className="mt-6 mb-8 w-full max-w-lg">
              <div
                className="lets-button"
                id="lets_widgetButton_hwn4AYoQ3WlgA0A4"
                style={{ maxWidth: "480px", height: "56px", margin: "0 auto" }}
              >
                <iframe
                  src="https://letsexchange.io/v2/widget-button?affiliate_id=hwn4AYoQ3WlgA0A4&is_iframe=true"
                  width="100%"
                  height="100%"
                  frameBorder="0"
                  allow="clipboard-read; clipboard-write"
                  title="Let's Exchange Button"
                ></iframe>
              </div>
            </div> */}

            {/* CTA Card */}
            {/* <div className="mt-8 w-full max-w-lg px-6 py-5 bg-white dark:bg-gray-900 rounded-3xl border-2 border-[#000000] border-b-[8px] border-r-[6px] text-center">
              <h2 className="text-2xl font-bold text-slate-700 dark:text-white">
                Start Your Crypto Journey
              </h2>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Join millions of users worldwide on Bitget and access an
                extensive range of cryptocurrencies. Safe, fast, and reliable.
              </p>
              <a
                href="https://www.bitget.com/fr/spot/MASUSDT"
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  boxShadow: "rgba(14, 14, 44, 0.4) 0px -1px 0px 0px inset",
                }}
                className="inline-block mt-5  bg-[#F6C955] border-b-[6px] border-2 border-[#000000] text-[#333333] font-medium px-4 py-2 rounded-lg hover:bg-[#FDE68A] transition-all duration-200 ease-in-out"
              >
                Visit Bitget Now
              </a>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuyCrypto;
