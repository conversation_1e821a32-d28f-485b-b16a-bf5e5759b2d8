import {
  Args,
  // bytesToStr,
  // formatUnits,
  Mas,
  Operation,
  parseMas,
  parseUnits,
  // Provider,
  SmartContract,
} from "@massalabs/massa-web3";
// import { Pool } from "../lib/structs/pool";
// import { Token } from "../redux/features/tokensSlice";
// import { IBestPool } from "../lib/types";
import { computeMintStorageCost } from "../lib/utils";
// import { FEES_SCALING_FACTOR } from "../lib/data";

export async function getLPBalance(
  poolContract: SmartContract,
  userAddress: string
): Promise<bigint> {
  const lpBalance = new Args(
    (
      await poolContract.read(
        "getLPBalance",
        new Args().addString(userAddress).serialize()
      )
    ).value
  ).nextU256();

  return lpBalance;
}

// export async function addLiquidity(
//   poolContract: SmartContract,
//   aAmount: string,
//   bAmount: string,
//   minAmountA: number,
//   minAmountB: number,
//   tokenADecimals: number,
//   tokenBDecimals: number
// ) {
//   const operation = await poolContract.call(
//     "addLiquidity",
//     new Args()
//       .addU256(parseUnits(aAmount, tokenADecimals))
//       .addU256(parseUnits(bAmount, tokenBDecimals))
//       .addU256(parseUnits(minAmountA.toString(), 18))
//       .addU256(parseUnits(minAmountB.toString(), 18))
//       .serialize(),
//     { coins: Mas.fromString("0.1") }
//   );

//   const status = await operation.waitSpeculativeExecution();

//   if (status === OperationStatus.SpeculativeSuccess) {
//     console.log("Liquidity added");
//   } else {
//     console.log("Status:", status);
//     console.log("Error:", await operation.getSpeculativeEvents());
//     throw new Error("Failed to add liquidity");
//   }
// }
// export async function addLiquidityWithMAS(
//   poolContract: SmartContract,
//   aAmount: string,
//   bAmount: string,
//   minAmountA: number,
//   minAmountB: number,
//   tokenADecimals: number,
//   tokenBDecimals: number
// ) {
//   console.log(
//     `Add liquidity with MAS: ${aAmount} A, ${bAmount} B (min: ${minAmountA} A, ${minAmountB} B) to pool...`
//   );

//   const storageCosts = computeMintStorageCost(poolContract.address);

//   const coins =
//     Mas.fromString(bAmount) + BigInt(storageCosts) + parseMas("0.1");

//   console.log("Coins To send: ", coins);

//   const operation = await poolContract.call(
//     "addLiquidityWithMas",
//     new Args()
//       .addU256(parseUnits(aAmount, tokenADecimals))
//       .addU256(parseUnits(bAmount, tokenBDecimals))
//       .addU256(parseUnits(minAmountA.toString(), 18))
//       .addU256(parseUnits(minAmountB.toString(), 18))
//       .serialize(),
//     { coins }
//   );

//   const status = await operation.waitSpeculativeExecution();

//   if (status === OperationStatus.SpeculativeSuccess) {
//     console.log("Liquidity added with MAS");
//   } else {
//     console.log("Status:", status);
//     console.log(
//       "events addd liquidity with mas",
//       await operation.getSpeculativeEvents()
//     );
//     throw new Error("Failed to add liquidity");
//   }

//   return coins;
// }
// export async function removeLiquidity(
//   poolContract: SmartContract,
//   lpAmount: bigint,
//   minAmountA: number,
//   minAmountB: number,
//   tokenADecimals: number,
//   tokenBDecimals: number
// ) {
//   try {
//     const operation = await poolContract.call(
//       "removeLiquidity",
//       new Args()
//         .addU256(lpAmount)
//         .addU256(parseUnits(minAmountA.toString(), tokenADecimals))
//         .addU256(parseUnits(minAmountB.toString(), tokenBDecimals))
//         .serialize(),
//       { coins: Mas.fromString("0.1") }
//     );

//     console.log("Operation ID :", operation.id);

//     const status = await operation.waitSpeculativeExecution();

//     if (status !== OperationStatus.SpeculativeSuccess) {
//       console.log("Status:", status);
//       // get speculative eventsr
//       const speculativeEvents = await operation.getSpeculativeEvents();
//       console.log("Speculative events:", speculativeEvents);
//       throw new Error(`Transaction failed with status: ${status}`);
//     }

//     console.log("Liquidity removed");
//   } catch (error) {
//     console.error("Error in removeLiquidity:", error);
//     throw new Error(
//       typeof error === "string" ? error : "Failed to remove liquidity"
//     );
//   }
// }

export async function addLiquidity(
  poolContract: SmartContract,
  aAmount: bigint,
  bAmount: bigint,
  minAmountA: bigint,
  minAmountB: bigint
): Promise<Operation> {
  return poolContract.call(
    "addLiquidity",
    new Args()
      .addU256(aAmount)
      .addU256(bAmount)
      .addU256(minAmountA)
      .addU256(minAmountB)
      .serialize(),
    { coins: Mas.fromString("0.1") }
  );
}

export async function addLiquidityWithMAS(
  poolContract: SmartContract,
  aAmount: bigint,
  bAmount: bigint,
  minAmountA: bigint,
  minAmountB: bigint
): Promise<Operation> {
  // Return Operation instead of void
  const storageCosts = computeMintStorageCost(poolContract.address);
  const coins = bAmount + BigInt(storageCosts) + parseMas("0.1");

  return poolContract.call(
    "addLiquidityWithMas",
    new Args()
      .addU256(aAmount)
      .addU256(bAmount)
      .addU256(minAmountA)
      .addU256(minAmountB)
      .serialize(),
    { coins }
  );
}

export async function removeLiquidity(
  poolContract: SmartContract,
  lpAmount: bigint,
  minAmountA: number,
  minAmountB: number,
  tokenADecimals: number,
  tokenBDecimals: number
): Promise<Operation> {
  // Return Operation instead of void
  return poolContract.call(
    "removeLiquidity",
    new Args()
      .addU256(lpAmount)
      .addU256(parseUnits(minAmountA.toString(), tokenADecimals))
      .addU256(parseUnits(minAmountB.toString(), tokenBDecimals))
      .serialize(),
    { coins: Mas.fromString("0.1") }
  );
}

export async function getReserveA(
  poolContract: SmartContract
): Promise<bigint> {
  const reserveA = new Args(
    (await poolContract.read("getLocalReserveA", new Args().serialize())).value
  ).nextU256();

  return reserveA;
}

export async function getReserveB(
  poolContract: SmartContract
): Promise<bigint> {
  const reserveB = new Args(
    (await poolContract.read("getLocalReserveB", new Args().serialize())).value
  ).nextU256();

  return reserveB;
}

export async function calculatePoolLiquidity(
  reserveA: number,
  reserveB: number
): Promise<number> {
  const product = reserveA * reserveB;

  const sqrtProduct = Math.sqrt(product);

  return sqrtProduct;
}

// export async function getPools(
//   registryContract: SmartContract,
//   provider: Provider
// ): Promise<Pool[]> {
//   const keys = await provider.getStorageKeys(
//     registryContract.address,
//     "pools::",
//     false
//   );

//   const poolsKeys = [];

//   for (const key of keys) {
//     const deserializedKey = bytesToStr(key);
//     poolsKeys.push(deserializedKey);
//   }

//   console.log("Pools keys:", poolsKeys);

//   const pools = [];

//   for (const key of poolsKeys) {
//     const pool = await getPoolByKey(registryContract, provider, key);
//     pools.push(pool);
//   }

//   return pools;
// }

// export async function getPoolByKey(
//   registryContract: SmartContract,
//   provider: Provider,
//   key: string
// ) {
//   const poolResult = await provider.readStorage(
//     registryContract.address,
//     [key],
//     false
//   );

//   const pool = new Args(poolResult[0]).nextSerializable<Pool>(Pool);

//   return pool;
// }

// export async function getBestPoolToUseOnSwap(
//   registryContract: SmartContract,
//   userProvider: Provider,
//   fromToken: Token,
//   toToken: Token
// ): Promise<IBestPool | null> {
//   console.log(
//     "Get the best pool to use on swap based on the token addresses and lqiudiity...."
//   );

//   const fromTokenAddess = fromToken.address;
//   const toTokenAddress = toToken.address;

//   const fromTokenDecimals = fromToken.decimals;
//   const toTokenDecimals = toToken.decimals;

//   console.log("From token address:", fromTokenAddess);
//   console.log("To token address:", toTokenAddress);

//   console.log("From token decimals:", fromTokenDecimals);
//   console.log("To token decimals:", toTokenDecimals);

//   const pools = await getPools(registryContract, userProvider);

//   console.log("Pools:", pools);

//   // Find pools that have both tokens (no matter the order)
//   const poolsWithTokens = pools.filter((pool) => {
//     return (
//       (pool.aTokenAddress === fromTokenAddess &&
//         pool.bTokenAddress === toTokenAddress) ||
//       (pool.bTokenAddress === fromTokenAddess &&
//         pool.aTokenAddress === toTokenAddress)
//     );
//   });

//   console.log("Pools with tokens:", poolsWithTokens);

//   // If there are no pools with both tokens, return null
//   if (poolsWithTokens.length === 0) {
//     return null;
//   }

//   // Find the pool with the highest liquidity by using calculatePoolLiquidity on each pool
//   let bestPool: IBestPool | null = null;

//   let highestLiquidity = 0;

//   for (const pool of poolsWithTokens) {
//     const poolContract = new SmartContract(userProvider, pool.poolAddress);

//     const reserveA = await getReserveA(poolContract);
//     const reserveB = await getReserveB(poolContract);

//     const tokenADecimals =
//       fromTokenAddess === pool.aTokenAddress
//         ? fromTokenDecimals
//         : toTokenDecimals;
//     const tokenBDecimals =
//       fromTokenAddess === pool.bTokenAddress
//         ? fromTokenDecimals
//         : toTokenDecimals;

//     const reserveANum = Number(formatUnits(reserveA, tokenADecimals));
//     const reserveBNum = Number(formatUnits(reserveB, tokenBDecimals));

//     console.log("Reserve A:", reserveANum);
//     console.log("Reserve B:", reserveBNum);

//     const liquidity = await calculatePoolLiquidity(reserveANum, reserveBNum);

//     console.log("Liquidity:", liquidity);

//     if (liquidity > highestLiquidity) {
//       bestPool = {
//         poolAddress: pool.poolAddress,
//         aTokenAddress: pool.aTokenAddress,
//         bTokenAddress: pool.bTokenAddress,
//         aReserve: reserveANum,
//         bReserve: reserveBNum,
//         liquidity: liquidity,
//       };
//       highestLiquidity = liquidity;
//     }
//   }

//   console.log("Best pool:", bestPool);
//   console.log("Highest liquidity:", highestLiquidity);

//   return bestPool;
// }

// const tokenCache = new Map<string, Token>();
// export const processPoolTokens = async (
//   pools: Pool[],
//   provider: any,
//   existingTokens: Token[]
// ): Promise<{ poolsData: any[]; processedTokens: Token[] }> => {
//   const poolsData: any[] = [];

//   // Get MAS/WMAS from initial state
//   const [MAS, WMAS] = existingTokens.filter(
//     (t) => t.symbol === "MAS" || t.symbol === "WMAS"
//   );

//   // MAS/WMAS first
//   if (MAS) tokenCache.set(MAS.address, MAS);
//   if (WMAS) tokenCache.set(WMAS.address, WMAS);

//   // collect all unique tokens from da pools
//   for (const pool of pools) {
//     if (!tokenCache.has(pool.aTokenAddress)) {
//       const token = await getTokenDetails(
//         pool.aTokenAddress,
//         provider,
//         existingTokens
//       );
//       if (token) tokenCache.set(pool.aTokenAddress, token);
//     }
//     if (!tokenCache.has(pool.bTokenAddress)) {
//       const token = await getTokenDetails(
//         pool.bTokenAddress,
//         provider,
//         existingTokens
//       );
//       if (token) tokenCache.set(pool.bTokenAddress, token);
//     }
//   }

//   // create pool data using cached tokens
//   for (let i = 0; i < pools.length; i++) {
//     const pool = pools[i];
//     const aToken = tokenCache.get(pool.aTokenAddress);
//     const bToken = tokenCache.get(pool.bTokenAddress);

//     if (!aToken || !bToken) continue;

//     poolsData.push({
//       id: i,
//       poolName: `${aToken.symbol} / ${bToken.symbol}`,
//       aTokenSymbol: aToken.symbol,
//       bTokenSymbol: bToken.symbol,
//       logos: [aToken.logo, bToken.logo],
//       tokenAddresses: [pool.aTokenAddress, pool.bTokenAddress],
//       poolAddress: pool.poolAddress,
//       fee: `${pool.inputFeeRate / FEES_SCALING_FACTOR}%`,
//     });
//   }

//   console.log("token cache", tokenCache);

//   return {
//     poolsData,
//     processedTokens: Array.from(tokenCache.values()),
//   };
// };

// const getTokenDetails = async (
//   address: string,
//   provider: any,
//   existingTokens: Token[]
// ): Promise<Token | undefined> => {
//   try {
//     // Check existing tokens  checks
//     const existing = existingTokens.find((t) => t.address === address);
//     if (existing) return { ...existing };

//     // Fetch from chain if not found
//     const tokenContract = new MRC20(provider, address);
//     const [symbol, name, decimals, logo, totalSupply] = await Promise.all([
//       bytesToStr((await tokenContract.read("symbol")).value),
//       bytesToStr((await tokenContract.read("name")).value),
//       U8.fromBytes((await tokenContract.read("decimals")).value),
//       bytesToStr((await tokenContract.read("url")).value),
//       new Args((await tokenContract.read("totalSupply")).value).nextU256(),
//     ]);

//     console.log(`total supply of  ${name}: `, totalSupply);

//     // Use existance token logo if exist
//     const existingLogo =
//       existingTokens.find((t) => t.address === address)?.logo || "";

//     return {
//       symbol,
//       name,
//       logo: logo || existingLogo,
//       price: 0,
//       balance: 0,
//       address,
//       decimals: Number(decimals),
//       isNative: address === NATIVE_MAS_COIN_ADDRESS,
//       totalSupply: Number(formatUnits(totalSupply, Number(decimals))) || 0,
//     };
//   } catch (error) {
//     console.error(`Error fetching token details for ${address}:`, error);
//     return undefined;
//   }
// };

// export async function getSwapOutEstimation(
//   poolContract: SmartContract,
//   tokenInAddress: string,
//   amountIn: number,
//   tokenInDecimals: number,
//   tokenOutDecimals: number
// ) {
//   const result = await poolContract.read(
//     "getSwapOutEstimation",
//     new Args()
//       .addString(tokenInAddress)
//       .addU256(parseUnits(amountIn.toString(), tokenInDecimals))
//       .serialize()
//   );

//   const amountOut = new Args(result.value).nextU256();

//   return formatUnits(amountOut, tokenOutDecimals);
// }

// export async function getSwapInEstimation(
//   poolContract: SmartContract,
//   tokenOutAddress: string,
//   amountOut: number,
//   tokenOutDecimals: number,
//   tokenInDecimals: number
// ) {
//   const result = await poolContract.read(
//     "getSwapInEstimation",
//     new Args()
//       .addString(tokenOutAddress)
//       .addU256(parseUnits(amountOut.toString(), tokenOutDecimals))
//       .serialize()
//   );

//   const amountIn = new Args(result.value).nextU256();

//   return formatUnits(amountIn, tokenInDecimals);
// }

// export async function getAddLiquidityLPEstimation(
//   poolContract: SmartContract,
//   aAmount: number,
//   bAmount: number,
//   aDecimals: number,
//   bDecimals: number
// ) {
//   const result = await poolContract.read(
//     "getAddLiquidityLPEstimation",
//     new Args()
//       .addU256(parseUnits(aAmount.toString(), aDecimals))
//       .addU256(parseUnits(bAmount.toString(), bDecimals))
//       .serialize()
//   );

//   const lpEstimated = new Args(result.value).nextU256();

//   console.log("lpEstimated", lpEstimated);

//   return lpEstimated;
// }

// services/Liquidity.ts
// export const fetchPools = createAsyncThunk(
//   "liquidityTokens/fetchPools",
//   async (forceRefresh: boolean = false, { getState, dispatch }) => {
//     const state = getState() as RootState;
//     const { lastUpdated } = state.liquidityTokens;
//     const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

//     // Return cached data if it's still valid
//     if (!forceRefresh && Date.now() - lastUpdated < CACHE_TTL) {
//       return state.liquidityTokens.pools;
//     }

//     const { connectedAccount } = useAccountStore.getState();
//     if (!connectedAccount) throw new Error("Wallet not connected");

//     const contract = new SmartContract(
//       connectedAccount,
//       import.meta.env.VITE_REGISTRY_CONTRACT_ADDRESS
//     );

//     const pools = await getPools(contract, connectedAccount);

//     const { poolsData, processedTokens } = await processPoolTokens(
//       pools,
//       connectedAccount,
//       state.liquidityTokens.tokens
//     );
//     dispatch(setLiquidityTokens(processedTokens));
//     const masToken = processedTokens.find(
//       (t) => t.address === NATIVE_MAS_COIN_ADDRESS
//     );
//     if (masToken) {
//       try {
//         const response = await fetch("https://explorer-api.massa.net/info");
//         const data = await response.json();
//         const totalSupply = data.total_supply;
//         const circulatingSupply = data.circulating_supply;
//         dispatch(
//           updateLiquidityTokenTotalSupply({
//             address: NATIVE_MAS_COIN_ADDRESS,
//             totalSupply,
//           })
//         );
//         dispatch(
//           updateLiquidityTokenCirculatingSupply({
//             address: NATIVE_MAS_COIN_ADDRESS,
//             circulatingSupply,
//           })
//         );
//       } catch (error) {
//         console.error("Error fetching MAS total supply:", error);
//       }
//     }

//     //Calculate token prices from pools
//     const wmasToken = processedTokens.find((t) => t.symbol === "WMAS")!;

//     for (const token of processedTokens) {
//       if (token.symbol === "WMAS" || token.symbol === "MAS") continue;

//       // Find pool with WMAS
//       const pool = pools.find(
//         (p) =>
//           (p.aTokenAddress === token.address &&
//             p.bTokenAddress === wmasToken.address) ||
//           (p.bTokenAddress === token.address &&
//             p.aTokenAddress === wmasToken.address)
//       );

//       if (pool) {
//         const poolContract = new SmartContract(
//           connectedAccount,
//           pool.poolAddress
//         );

//         try {
//           const [reserveA, reserveB] = await Promise.all([
//             getReserveA(poolContract),
//             getReserveB(poolContract),
//           ]);

//           // Determine reserves orientation
//           const isTokenA = pool.aTokenAddress === token.address;
//           const tokenDecimals = isTokenA ? token.decimals : wmasToken.decimals;
//           const wmasDecimals = isTokenA ? wmasToken.decimals : token.decimals;

//           const tokenReserve = Number(
//             formatUnits(isTokenA ? reserveA : reserveB, tokenDecimals)
//           );

//           const wmasReserve = Number(
//             formatUnits(isTokenA ? reserveB : reserveA, wmasDecimals)
//           );

//           if (tokenReserve > 0 && wmasReserve > 0) {
//             const price = (wmasReserve / tokenReserve) * wmasToken.price;
//             dispatch(
//               updateLiquidityTokenPrice({
//                 symbol: token.symbol,
//                 price: Number(price.toFixed(6)), // Precision to 6 decimals
//               })
//             );
//           }
//         } catch (error) {
//           console.error(`Error fetching reserves for ${token.symbol}:`, error);
//         }
//       }
//     }

//     return poolsData;
//   }
// );

export async function getLPTotalSupply(poolContract: SmartContract) {
  const result = await poolContract.read(
    "getLPTotalSupply",
    new Args().serialize()
  );

  const amountOut = new Args(result.value).nextU256();

  return amountOut;
}
