import axiosInstance from "./axios/axiosInstance";

interface ErrorLog {
  error: string;
  operation_id: string | null;
  user_address: string | null;
}

export const logError = async (
  error: any,
  operationId: string | null = null,
  userAddress: string | null = null
) => {
  try {
    let errorMessage = "Unknown error";

    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === "string") {
      errorMessage = error;
    } else if (error?.response?.data?.error) {
      errorMessage = error.response.data.error;
    }

    const payload: ErrorLog = {
      error: errorMessage,
      operation_id: operationId,
      user_address: userAddress,
    };

    await axiosInstance.post("/errors/store", payload);
  } catch (loggingError) {
    console.error("Failed to log error:", loggingError);
  }
};
export function prettyPrice(
  raw: number | null | undefined,
  currency: "USD" | "MAS" = "USD",
  minSig = 4,
  markup = true,
  showSymbol = true
): React.ReactNode | string {
  if (raw == null || !Number.isFinite(raw)) return "N/A";

  const prefix = showSymbol && currency === "USD" ? "$" : "";
  const suffix = showSymbol && currency === "MAS" ? " MAS" : "";

  // For numbers >= 0.01
  if (raw >= 0.01) {
    return (
      prefix +
      raw.toLocaleString(undefined, { maximumFractionDigits: 4 }) +
      suffix
    );
  }
  // For numbers between 0.0001 and 0.01
  else if (raw >= 0.0001) {
    return (
      prefix +
      raw.toLocaleString(undefined, {
        minimumFractionDigits: 6,
        maximumFractionDigits: 8,
      }) +
      suffix
    );
  }
  // For numbers < 0.0001
  else {
    const fixed = raw.toFixed(30); // Use sufficient precision
    const frac = fixed.split(".")[1] || "";
    const leadingZeros = frac.search(/[^0]/); // Find first non-zero digit
    if (leadingZeros === -1) return `${prefix}0${suffix}`; // Handle zero case

    const sigStart = leadingZeros;
    const sig = frac.slice(sigStart, sigStart + minSig).padEnd(minSig, "0");

    if (!markup) {
      const formatted = `0.${"0".repeat(leadingZeros)}${sig}`;
      return `${prefix}${formatted}${suffix}`;
    }

    return (
      <span>
        {prefix}0.0<sub className="number-value">{leadingZeros}</sub>
        {sig}
        {suffix}
      </span>
    );
  }
}
export function formatScientificToFixed(
  value: number,
  maxDecimals: number = 18
): string {
  // Clamp maxDecimals between 0 and 100
  maxDecimals = Math.max(0, Math.min(maxDecimals, 100));

  // Handle non-scientific notation numbers (e.g., 123.45)
  if (!value.toString().includes("e")) {
    return value.toFixed(maxDecimals);
  }

  const [, exponent] = value.toExponential().split("e");
  const expNum = parseInt(exponent);

  // For large numbers (e.g., 1.23e+5), just format with maxDecimals
  if (expNum >= 0) {
    return value.toFixed(maxDecimals);
  }

  // For small numbers (e.g., 2.75e-9), calculate safe decimal places
  const decimalPlaces = Math.min(-expNum, maxDecimals);
  return value.toFixed(decimalPlaces);
}

export const withRetry = async <T,>(
  fn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise((resolve) => setTimeout(resolve, delay * (i + 1)));
    }
  }
  throw new Error("Max retries exceeded");
};
