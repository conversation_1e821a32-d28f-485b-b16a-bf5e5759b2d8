// CreatePool.tsx
import { useState, useRef, useEffect, useCallback } from "react";
import { useAccountStore } from "@massalabs/react-ui-kit";
import { toast } from "react-toastify";
import {
  Args,
  Mas,
  MRC20,
  parseUnits,
  SmartContract,
  byteToBool,
  bytesToStr,
  U8,
  formatUnits,
  parseMas,
  Operation,
  Multicall,
  Call,
  MAX_GAS_EXECUTE,
} from "@massalabs/massa-web3";
import { useAppDispatch } from "../redux/store";
import {
  formatBalance,
  formatStringDecimals,
  NATIVE_MAS_COIN_ADDRESS,
  truncateDecimals,
  waitForExecution,
  WMAS_TOKEN_ADDRESS,
} from "../lib/utils";
import ConnectWalletModal from "../components/ConnectWalletModal";
import { fetchTokens, Token } from "../redux/features/tokensSlice";
import { FEES_SCALING_FACTOR } from "../lib/data";
import axiosInstance from "../lib/axios/axiosInstance";
import { logError } from "../lib/utils2";

const CreatePool: React.FC = () => {
  const dispatch = useAppDispatch();
  const { connectedAccount, currentWallet } = useAccountStore();
  const [network, setNetwork] = useState<string | null>(null);
  const [tokens, setTokens] = useState<Token[]>([]);
  const [toggleConnectWalletModal, setToggleConnectWalletModal] =
    useState(false);
  const [fromToken, setFromToken] = useState<Token | null>(null);

  const [toToken, setToToken] = useState(
    tokens.find((t) => t?.symbol === "WMAS")!
  );
  const [fromAmount, setFromAmount] = useState<string>("");
  const [fromPrice, setFromPrice] = useState<string>("");
  const [toAmount, setToAmount] = useState<string>("");

  const [showFromDropdown, setShowFromDropdown] = useState(false);
  const [showToDropdown, setShowToDropdown] = useState(false);
  const fromRef = useRef<HTMLDivElement>(null);
  const toRef = useRef<HTMLDivElement>(null);

  const [insufficientBalance, setInsufficientBalance] = useState(false);
  const [insufficientToBalance, setInsufficientToBalance] = useState(false);
  const [fromTokenBalance, setFromTokenBalance] = useState(0);
  const [toTokenBalance, setToTokenBalance] = useState(0);

  const [selectedFee, setSelectedFee] = useState("0.3%");
  const feeOptions = [
    { value: "0.01%", description: "Perfect for very stable pairs." },
    { value: "0.05%", description: "Best for stable pairs." },
    { value: "0.3%", description: "Best for most pairs." },
    { value: "1%", description: "Ideal for exotic pairs." },
  ];

  useEffect(() => {
    const fetchNetworkInfo = async () => {
      if (connectedAccount && currentWallet) {
        const networkInfo = await currentWallet.networkInfos();
        const networkName = networkInfo?.name ?? null;
        setNetwork(networkName);
      } else {
        setNetwork(null);
      }
    };

    fetchNetworkInfo();

    const handleNetworkChange = async () => {
      await fetchNetworkInfo();
    };

    if (currentWallet) {
      currentWallet.listenNetworkChanges(handleNetworkChange);
    }
  }, [connectedAccount, currentWallet]);

  const fetchTokenBalance = useCallback(
    async (token: Token | null) => {
      if (!connectedAccount || !token) return 0;

      try {
        let balance: number;
        if (token.is_native) {
          const masBalance = await connectedAccount.balance(false);
          balance = Number(formatUnits(masBalance, token.decimals));
        } else {
          const tokenContract = new MRC20(
            connectedAccount as any,
            token.address
          );
          const rawBalance = await tokenContract.balanceOf(
            connectedAccount.address
          );
          balance = Number(formatUnits(rawBalance, token.decimals));
        }
        return balance;
      } catch (error) {
        console.error("Error fetching balance:", error);
        return 0;
      }
    },
    [connectedAccount]
  );

  useEffect(() => {
    const updateBalances = async () => {
      if (fromToken) {
        const balance = await fetchTokenBalance(fromToken);
        setFromTokenBalance(balance);
      }
      if (toToken) {
        const balance = await fetchTokenBalance(toToken);
        setToTokenBalance(balance);
      }
    };
    updateBalances();
  }, [fromToken, toToken, fetchTokenBalance]);

  useEffect(() => {
    const parsedFromAmount = parseFloat(fromAmount || "0");
    const parsedToAmount = parseFloat(toAmount || "0");

    setInsufficientBalance(parsedFromAmount > fromTokenBalance);
    setInsufficientToBalance(parsedToAmount > toTokenBalance);
  }, [fromAmount, toAmount, fromTokenBalance, toTokenBalance]);

  // Fetch token details from blockchain
  const getTokenDetails = useCallback(
    async (address: string): Promise<any> => {
      if (!connectedAccount) return;

      try {
        const tokenContract = new MRC20(connectedAccount as any, address);
        const [symbol, name, decimals] = await Promise.all([
          bytesToStr((await tokenContract.read("symbol")).value),
          bytesToStr((await tokenContract.read("name")).value),
          U8.fromBytes((await tokenContract.read("decimals")).value),
        ]);

        return {
          address,
          symbol,
          name,
          decimals: Number(decimals),
          balance: 0,
        };
      } catch (error) {
        console.error("Error fetching token details:", error);
        return undefined;
      }
    },
    [connectedAccount]
  );

  const [searchQuery, setSearchQuery] = useState("");
  const [searchedTokens, setSearchedTokens] = useState<Token[]>([]);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);

  // Handle token search
  useEffect(() => {
    const searchTokens = async () => {
      if (!searchQuery.trim()) {
        setSearchedTokens([]);
        return;
      }

      setIsSearching(true);
      try {
        // First check backend
        const response = await axiosInstance.get(
          `/tokens?search=${searchQuery}`
        );

        if (response.data.length > 0) {
          setSearchedTokens(response.data);
          setSearchError(null);
        } else {
          // If no results from backend, check blockchain
          const token = await getTokenDetails(searchQuery);
          if (token) {
            setSearchedTokens([token]);
            setSearchError(null);
          } else {
            setSearchedTokens([]);
            setSearchError("Token not found on blockchain");
          }
        }
      } catch (error) {
        console.error("Search error:", error);
        setSearchError("Error searching for token");
        setSearchedTokens([]);
      }
      setIsSearching(false);
    };

    const debounceTimer = setTimeout(searchTokens, 500);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery, getTokenDetails]);

  // Handle token import
  const handleImportToken = (token: Token) => {
    const exists = tokens.some(
      (t) => t.address.toLowerCase() === token.address.toLowerCase()
    );
    if (!exists) {
      setTokens((prev) => [...prev, token]);
    }
    handleSelectFromToken(token);
    setSearchQuery("");
  };

  useEffect(() => {
    setToToken(tokens.find((t) => t?.symbol === "WMAS")!);
  }, [tokens]);

  const [tokensLoaded, setTokensLoaded] = useState(false);

  const fetchPortfolioData = async () => {
    try {
      const response = await axiosInstance.get(`/tokens`);
      const backendTokens = response.data;
      setTokens((prevTokens) => {
        // Merge previous tokens with backend tokens, avoiding duplicates
        const mergedTokens = [...prevTokens];
        backendTokens.forEach((backendToken: Token) => {
          const exists = mergedTokens.some(
            (t) =>
              t.address.toLowerCase() === backendToken.address.toLowerCase()
          );
          if (!exists) {
            mergedTokens.push(backendToken);
          }
        });
        return mergedTokens;
      });
      setTokensLoaded(true); // Mark tokens as loaded
    } catch (err) {
      console.error("Error fetching portfolio:", err);
    }
  };

  const renderTokenADropdown = () => (
    <div className="eagle-dropdown absolute top-full mt-2 -left-14 w-[160%] bg-white border-2 border-[#1E1E1E] rounded-md shadow-lg z-50 overflow-y-auto max-h-60">
      <div className="sticky top-0 bg-white p-2 border-b border-[#1E1E1E]">
        <input
          type="text"
          placeholder="Search name or paste address"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full p-2 text-sm border border-[#1E1E1E] rounded-lg focus:outline-none focus:ring-1 focus:ring-slate-500"
        />
      </div>

      {isSearching ? (
        <div className="p-3 text-gray-500">Searching...</div>
      ) : searchQuery ? (
        searchedTokens.length > 0 ? (
          searchedTokens.map((token) => {
            const existsInList = tokens.some(
              (t) => t.address === token.address
            );
            return (
              <div
                key={token.address}
                className="flex items-center justify-between gap-3 p-3 hover:bg-[#f1f5f9] cursor-pointer border-b border-[#1E1E1E]"
                onClick={() => {
                  if (existsInList) {
                    handleSelectFromToken(token);
                    setShowFromDropdown(false);
                  }
                }}
              >
                <div className="flex items-center gap-3">
                  {token.logo ? (
                    <img
                      src={token.logo}
                      alt={token.symbol}
                      className="w-6 h-6 rounded-full"
                    />
                  ) : (
                    <div className="w-6 h-6 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold">
                      {token.symbol.slice(0, 3).toUpperCase()}
                    </div>
                  )}
                  <div>
                    <span className="font-medium text-black">
                      {token.symbol}
                    </span>
                    <small className="block text-gray-400">{token.name}</small>
                  </div>
                </div>
                {!existsInList && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleImportToken(token);
                    }}
                    className="px-3 py-1 text-sm bg-[#1E1E1E] text-[#FFFFFF] rounded-lg hover:opacity-90"
                  >
                    Import
                  </button>
                )}
              </div>
            );
          })
        ) : (
          <div className="p-3 text-gray-500">
            {searchError || "No tokens found"}
          </div>
        )
      ) : (
        tokens
          .filter(
            (token) =>
              token?.symbol !== toToken?.symbol &&
              token?.symbol !== "MAS" &&
              token?.symbol !== "WMAS"
          )
          .map((token) => {
            const showLogo = token?.logo && token?.logo.trim() !== "";
            const fallbackText = token?.symbol.slice(0, 3).toUpperCase();

            return (
              <div
                key={token?.symbol}
                onClick={() => handleSelectFromToken(token)}
                className="flex items-center gap-3 p-3 hover:bg-[#f1f5f9] cursor-pointer border-b border-[#1E1E1E]"
              >
                {showLogo ? (
                  <img
                    src={token?.logo}
                    alt={token?.symbol}
                    className="w-6 h-6 rounded-full object-contain"
                  />
                ) : (
                  <div className="w-6 h-6 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold">
                    {fallbackText}
                  </div>
                )}
                <div>
                  <span className="font-medium text-black">
                    {token?.symbol}
                  </span>
                  <small className="block text-gray-400">{token?.name}</small>
                </div>
              </div>
            );
          })
      )}
    </div>
  );

  useEffect(() => {
    fetchPortfolioData();
  }, [connectedAccount?.address]);

  // Parse values
  const parsedFromAmount = parseFloat(fromAmount || "0");
  const parsedFromPrice = parseFloat(fromPrice || "0");
  const toTokenPrice = toToken?.price?.usd || 0;

  // Track pool existence
  const [poolExists, setPoolExists] = useState<boolean | null>(null);

  useEffect(() => {
    dispatch(fetchTokens());
  }, [dispatch]);

  useEffect(() => {
    const handleTokenParam = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const tokenAddress = urlParams.get("token");

      if (tokenAddress && tokensLoaded) {
        // Only process after tokens are loaded
        try {
          // Check if token exists (case-insensitive)
          const existingToken = tokens.find(
            (t) => t.address.toLowerCase() === tokenAddress.toLowerCase()
          );
          if (existingToken) {
            setFromToken(existingToken);
            return;
          }

          // Fetch token details from blockchain
          const tokenDetails = await getTokenDetails(tokenAddress);
          if (tokenDetails) {
            // Check again if token was added during fetch
            const existsNow = tokens.some(
              (t) =>
                t.address.toLowerCase() === tokenDetails.address.toLowerCase()
            );
            if (!existsNow) {
              setTokens((prev) => [...prev, tokenDetails]);
            }
            setFromToken(tokenDetails);
          }
        } catch (error) {
          toast.error("Failed to load token from address");
        }
      }
    };

    handleTokenParam();
  }, [tokens, getTokenDetails, tokensLoaded]);

  useEffect(() => {
    if (parsedFromAmount > 0 && parsedFromPrice > 0 && toTokenPrice > 0) {
      const totalUsd = parsedFromAmount * parsedFromPrice;
      const computedToToken = totalUsd / toTokenPrice;
      setToAmount(computedToToken.toString());
    } else {
      setToAmount("");
    }
  }, [parsedFromAmount, parsedFromPrice, toTokenPrice]);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (fromRef.current && !fromRef.current.contains(e.target as Node)) {
        setShowFromDropdown(false);
      }
      if (toRef.current && !toRef.current.contains(e.target as Node)) {
        setShowToDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const [buttonText, setButtonText] = useState("");
  const [buttonDisabled, setButtonDisabled] = useState(false);

  useEffect(() => {
    if (!connectedAccount) {
      setButtonText("Connect Wallet");
      setButtonDisabled(false);
    } else {
      const hasInsufficientBalance = parseFloat(fromAmount) > fromTokenBalance;
      const hasValidInputs =
        fromToken && toToken && fromAmount && !hasInsufficientBalance;
      const shouldDisable =
        !hasValidInputs || hasInsufficientBalance || poolExists === true;

      setButtonText(hasValidInputs ? "Create Pool" : "Enter Amounts");
      setButtonDisabled(shouldDisable);
    }
  }, [
    connectedAccount,
    fromToken,
    toToken,
    fromAmount,
    fromTokenBalance,
    poolExists,
  ]);

  /**
   * Check if pool already exists on-chain
   */
  async function checkPoolExists(
    aTokenAddress: string,
    bTokenAddress: string,
    feeRate: number
  ) {
    if (!connectedAccount) return;

    const registryContractAddress = import.meta.env
      .VITE_REGISTRY_CONTRACT_ADDRESS;
    const contract = new SmartContract(
      connectedAccount as any,
      registryContractAddress
    );

    try {
      const operation = await contract.read(
        "isPoolExists",
        new Args()
          .addString(aTokenAddress)
          .addString(bTokenAddress)
          .addU64(BigInt(feeRate * FEES_SCALING_FACTOR))
          .serialize()
      );
      const returnedData = byteToBool(operation.value);
      const result = returnedData;
      console.log("result from check pool exist", result);
      setPoolExists(result);
    } catch (error) {
      console.error("Error checking pool existence:", error);
      setPoolExists(null);
    }
  }

  const handleCreatePool = async () => {
    try {
      if (!connectedAccount) {
        throw new Error("Please connect your wallet.");
      }
      if (!fromToken || !toToken) {
        throw new Error("Invalid token selection.");
      }

      toast.loading("Creating pool...");
      const registryContractAddress = import.meta.env
        .VITE_REGISTRY_CONTRACT_ADDRESS;

      const contract = new SmartContract(
        connectedAccount,
        registryContractAddress
      );

      const aTokenAddress = fromToken.address || "";
      const bTokenAddress = toToken.address || "";
      const inputFeeRate = Number(selectedFee.replace("%", ""));

      if (parsedFromAmount === 0) {
        console.log("No fromAmount entered, calling createNewPool ...");

        const operation = await contract.call(
          "createNewPool",
          new Args()
            .addString(aTokenAddress)
            .addString(bTokenAddress)
            .addU64(BigInt(inputFeeRate * FEES_SCALING_FACTOR)) // your on-chain logic may vary
            .serialize(),
          { coins: Mas.fromString("10") }
        );

        const [, isSuccess] = await waitForExecution(operation);

        const speculativeEvents = await operation.getSpeculativeEvents();

        console.log("Events:", speculativeEvents);

        if (isSuccess) {
          const newFromBalance = await fetchTokenBalance(fromToken);
          const newToBalance = await fetchTokenBalance(toToken);
          setFromTokenBalance(newFromBalance);
          setToTokenBalance(newToBalance);
          toast.dismiss();
          toast.success(
            <div>
              Pool created successfully!
              <a
                href={
                  network?.toUpperCase() === "BUILDNET"
                    ? `https://www.massexplo.com/tx/${operation.id}?network=buildnet`
                    : `https://explorer.massa.net/mainnet/operation/${operation.id}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="block mt-2 text-blue-600 hover:underline"
              >
                View on Explorer
              </a>
            </div>
          );
          //  reset state

          setFromAmount("");
          setFromPrice("");
          setToAmount("");
          setFromToken(null);
          setToAmount("");
        } else {
          throw new Error("Failed to create new pool");
        }
      } else {
        console.log(
          "fromAmount present, calling createNewPoolWithLiquidity ..."
        );

        const isUsingMAS = toToken.address === NATIVE_MAS_COIN_ADDRESS;

        const amountA = parseUnits(
          formatStringDecimals(fromAmount, fromToken.decimals),
          fromToken.decimals
        );
        const amountB = parseUnits(
          formatStringDecimals(toAmount, toToken.decimals),
          toToken.decimals
        );

        console.log("Amount A:", amountA);
        console.log("Amount B:", amountB);

        const coinsToSend = isUsingMAS
          ? amountB + parseMas("12")
          : parseMas("12");

        console.log("Coins to send:", coinsToSend);
        console.log("Amount B:", amountB);
        console.log("Amount A:", amountA);

        // // Increase allowance for the 'From' token
        // if (allowanceA > 0 && aTokenAddress !== NATIVE_MAS_COIN_ADDRESS) {
        //   await increaseAllowance(
        //     aTokenAddress,
        //     registryContractAddress,
        //     amountA,
        //     fromToken.symbol
        //   );
        // }
        // // Increase allowance for the 'To' token if it's not the native MAS
        // if (allowanceB > 0 && bTokenAddress !== NATIVE_MAS_COIN_ADDRESS) {
        //   await increaseAllowance(
        //     bTokenAddress,
        //     registryContractAddress,
        //     amountB,
        //     toToken.symbol
        //   );
        // }
        // console.log("allowance A", allowanceA);
        // console.log("Allowance B", allowanceB);

        let isAAllownaceCall = false;
        let isBAllownaceCall = false;

        // Get token A allowance
        const tokenAContract = new MRC20(connectedAccount, aTokenAddress);

        const aAllowance = await tokenAContract.allowance(
          connectedAccount.address,
          registryContractAddress
        );

        console.log("allowanceA", aAllowance);

        // Check if allowance is not enough to add liquidity
        if (aAllowance < amountA) {
          isAAllownaceCall = true;
        }

        if (!isUsingMAS) {
          // Get token B allowance
          const tokenBContract = new MRC20(connectedAccount, bTokenAddress);

          const bAllowance = await tokenBContract.allowance(
            connectedAccount.address,
            registryContractAddress
          );

          console.log("allowanceB", bAllowance);

          if (bAllowance < amountB) {
            isBAllownaceCall = true;
          }
        }

        let operation: Operation;

        // If allowance is enough for both tokens, call createNewPoolWithLiquidity directly
        if (!isAAllownaceCall && !isBAllownaceCall) {
          operation = await contract.call(
            "createNewPoolWithLiquidity",
            new Args()
              .addString(aTokenAddress)
              .addString(bTokenAddress)
              .addU256(amountA)
              .addU256(amountB)
              .addU256(0n)
              .addU256(0n)
              .addU64(BigInt(inputFeeRate * FEES_SCALING_FACTOR))
              .addBool(isUsingMAS)
              .serialize(),
            {
              coins: coinsToSend,
            }
          );
        } else {
          const multicall = new Multicall(connectedAccount);

          const calls: Call[] = [];

          if (isAAllownaceCall) {
            const increaseAAllowanceArgs = new Args()
              .addString(registryContractAddress)
              .addU256(amountA);

            calls.push({
              targetContract: aTokenAddress,
              targetFunc: "increaseAllowance",
              callData: increaseAAllowanceArgs.serialize(),
              coins: parseMas("0.02"),
            });
          }

          if (isBAllownaceCall) {
            const increaseBAllowanceArgs = new Args()
              .addString(registryContractAddress)
              .addU256(amountB);

            calls.push({
              targetContract: bTokenAddress,
              targetFunc: "increaseAllowance",
              callData: increaseBAllowanceArgs.serialize(),
              coins: parseMas("0.02"),
            });
          }

          // Add the createNewPoolWithLiquidity call
          calls.push({
            targetContract: registryContractAddress,
            targetFunc: "createNewPoolWithLiquidity",
            callData: new Args()
              .addString(aTokenAddress)
              .addString(bTokenAddress)
              .addU256(amountA)
              .addU256(amountB)
              .addU256(0n)
              .addU256(0n)
              .addU64(BigInt(inputFeeRate * FEES_SCALING_FACTOR))
              .addBool(isUsingMAS)
              .serialize(),
            coins: coinsToSend,
          });

          console.log("calls", calls);

          operation = await multicall.execute(calls, {
            maxGas: MAX_GAS_EXECUTE,
          });
        }

        const [, isSuccess] = await waitForExecution(operation);
        const speculativeEvents = await operation.getSpeculativeEvents();
        console.log("Final events:", speculativeEvents);

        if (isSuccess) {
          const newFromBalance = await fetchTokenBalance(fromToken);
          const newToBalance = await fetchTokenBalance(toToken);
          setFromTokenBalance(newFromBalance);
          setToTokenBalance(newToBalance);
          toast.dismiss();
          toast.success(
            <div>
              Pool created successfully!
              <a
                href={
                  network?.toUpperCase() === "BUILDNET"
                    ? `https://www.massexplo.com/tx/${operation.id}?network=buildnet`
                    : `https://explorer.massa.net/mainnet/operation/${operation.id}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="block mt-2 text-blue-600 hover:underline"
              >
                View on Explorer
              </a>
            </div>
          );
          console.log("Pool created successfully with liquidity");
          // reset state
          setFromAmount("");
          setFromPrice("");
          setToAmount("");
          setFromToken(null);
          setToAmount("");
        } else {
          const errorEvent = speculativeEvents.find((event) =>
            event.data.includes("massa_execution_error")
          );
          const errorMessage = errorEvent
            ? JSON.parse(errorEvent.data).massa_execution_error
            : "Unknown error occurred";
          logError(
            errorMessage,
            operation.id,
            connectedAccount?.address || null
          ).catch((e) => console.error("Failed to log error", e));
          toast.dismiss();
          toast.error(
            <div>
              Error: {errorMessage}
              <a
                href={
                  network?.toUpperCase() === "BUILDNET"
                    ? `https://www.massexplo.com/tx/${operation.id}?network=buildnet`
                    : `https://explorer.massa.net/mainnet/operation/${operation.id}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="block mt-2 text-blue-600 hover:underline"
              >
                View on Explorer
              </a>
            </div>
          );
          console.error("Error creating pool:", errorMessage);
        }
      }
    } catch (error) {
      console.error("Error creating pool:", error);
      toast.dismiss();
      toast.error("Error creating pool");
    }
  };

  const handleSelectFromToken = async (token: any) => {
    setFromToken(token);
    setShowFromDropdown(false);

    // Reset pool existence before checking
    setPoolExists(null);

    // We only check if both tokens are defined and we have a connected account
    if (connectedAccount && token && toToken) {
      const fee = Number(selectedFee.replace("%", ""));
      await checkPoolExists(
        token.address,
        tokens.find((t) => t?.symbol === "WMAS")!?.address,
        fee
      );
    }
  };

  return (
    <>
      {/* Connect wallet modal */}
      <ConnectWalletModal
        toggleConnectWalletModal={toggleConnectWalletModal}
        setToggleConnectWalletModal={setToggleConnectWalletModal}
      />

      <div className="flex flex-col items-center w-full px-3 gap-y-6">
        {/* Header */}
        <div className="w-full max-w-6xl mt-1 sm:mt-14 flex flex-col gap-y-4" />

        {/* Container */}
        <div className="flex flex-col gap-3 p-6 bg-white rounded-3xl backdrop-blur-xl border-2 border-[#1E1E1E] mb-3 w-full max-w-6xl">
          <div className="flex items-center gap-2 text-3xl font-bold text-gray-800">
            Create Pool
          </div>
          <p className="text-base text-gray-600">
            The fastest and cheapest way to make your tokens tradable on
            EagleFi.
          </p>

          <div className="w-full max-w-6xl space-y-6">
            {/* TOKEN A (fromToken) AMOUNT */}
            <div className="rounded-lg p-4 border-2 bg-[#E6EBEC] text-[#6e7aaa] relative">
              <div className="flex items-center justify-between gap-4">
                <div>
                  <label
                    htmlFor="tokenA"
                    className="block mb-2 text-sm font-medium text-gray-700"
                  >
                    Token A
                  </label>
                  <input
                    type="number"
                    placeholder="0"
                    value={fromAmount}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value === "" || parseFloat(value) >= 0) {
                        setFromAmount(value);
                      }
                    }}
                    disabled={!connectedAccount || !fromToken}
                    className={`w-full bg-transparent text-2xl font-bold placeholder-gray-500 text-gray-700 focus:outline-none ${
                      !connectedAccount || !fromToken
                        ? "cursor-not-allowed"
                        : ""
                    }`}
                  />

                  {connectedAccount && (
                    <small className="text-gray-500">
                      Balance: {formatBalance(fromTokenBalance)}
                      {connectedAccount &&
                        fromTokenBalance > 0 &&
                        fromToken && (
                          <button
                            onClick={() => {
                              const formattedBalance = truncateDecimals(
                                fromTokenBalance,
                                fromToken.decimals
                              );
                              setFromAmount(formattedBalance);
                            }}
                            className="ml-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            Max
                          </button>
                        )}
                    </small>
                  )}
                </div>
                <div className="relative" ref={fromRef}>
                  <button
                    onClick={() => setShowFromDropdown(!showFromDropdown)}
                    className="text-white font-medium text-lg bg-[#1E1E1E] px-3 py-1 rounded-2xl flex items-center gap-2 border border-[#e3e6f1]"
                  >
                    {!fromToken ? (
                      <span className="text-white">Select token</span>
                    ) : fromToken?.logo && fromToken?.logo.trim() !== "" ? (
                      <>
                        <img
                          src={fromToken?.logo}
                          alt={fromToken?.symbol}
                          className="w-6 h-6 rounded-full"
                        />
                        {fromToken?.symbol}
                      </>
                    ) : fromToken ? (
                      <>
                        <div className="w-6 h-6 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold">
                          {fromToken?.symbol.slice(0, 3).toUpperCase()}
                        </div>
                        {fromToken?.symbol}
                      </>
                    ) : null}
                    <span className="pr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="white"
                        strokeWidth={4}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </span>
                  </button>

                  {/* {showFromDropdown && (
                    <div className="eagle-dropdown absolute top-full mt-2 -left-14 w-[160%] bg-white border border-[#e3e6f1] rounded-2xl shadow-lg z-50 max-h-64 overflow-auto">
                      {tokens
                        .filter(
                          (token) =>
                            token?.symbol !== toToken?.symbol &&
                            token?.symbol !== "MAS" &&
                            token?.symbol !== "WMAS"
                        )
                        .map((token) => {
                          const showLogo =
                            token?.logo && token?.logo.trim() !== "";
                          const fallbackText = token?.symbol
                            .slice(0, 3)
                            .toUpperCase();

                          return (
                            <div
                              key={token?.symbol}
                              onClick={() => handleSelectFromToken(token)}
                              className="flex items-center gap-3 p-3 hover:bg-[#f1f5f9] cursor-pointer"
                            >
                              {showLogo ? (
                                <img
                                  src={token?.logo}
                                  alt={token?.symbol}
                                  className="w-6 h-6 rounded-full object-contain"
                                />
                              ) : (
                                <div className="w-6 h-6 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold">
                                  {fallbackText}
                                </div>
                              )}
                              <div>
                                <span className="font-medium text-black">
                                  {token?.symbol}
                                </span>
                                <small className="block text-gray-400">
                                  {token?.name}
                                </small>
                              </div>
                            </div>
                          );
                        })}
                    </div>
                  )} */}
                  {showFromDropdown && renderTokenADropdown()}
                </div>
              </div>
              {/* Show a message if the pool already exists */}
              {poolExists === true && (
                <p className="text-red-500 text-sm mt-1">
                  A pool with this token pair and fee already exists.
                </p>
              )}
              {insufficientBalance && (
                <p className="text-red-500 text-sm mt-1">
                  Insufficient {fromToken?.symbol} balance.
                </p>
              )}
            </div>

            {/* TOKEN A PRICE */}
            <div className="p-4 rounded-lg  border-2 bg-[#E6EBEC] mb-0">
              <label
                htmlFor="tokenAPrice"
                className="block mb-2 text-sm font-medium text-gray-700"
              >
                Price Per Token A (USD)
              </label>
              <input
                id="tokenAPrice"
                type="number"
                placeholder="Price of Token A in USD"
                value={fromPrice}
                onChange={(e) => {
                  const val = e.target.value;
                  if (val === "" || parseFloat(val) >= 0) {
                    setFromPrice(val);
                  }
                }}
                className="w-full bg-transparent text-xl font-bold placeholder:text-base placeholder:font-normal placeholder-gray-500 text-gray-700 focus:outline-none"
              />
            </div>

            {/* TOKEN B (toToken) AMOUNT read-only */}
            <div className="p-4 rounded-lg border-2 bg-[#E6EBEC] relative">
              <div className="flex items-center justify-between gap-4">
                <div>
                  <label
                    htmlFor="tokenA"
                    className="block mb-2 text-sm font-medium text-gray-700"
                  >
                    Token B
                  </label>
                  <input
                    type="number"
                    placeholder="0"
                    value={toAmount}
                    readOnly
                    className="w-full bg-transparent text-2xl font-bold placeholder-gray-500 text-gray-700 focus:outline-none"
                  />
                  {connectedAccount && (
                    <small className="text-gray-500">
                      Balance: {formatBalance(toTokenBalance)}
                    </small>
                  )}
                </div>
                <div className="relative" ref={toRef}>
                  <button
                    onClick={() => setShowToDropdown(!showToDropdown)}
                    className="text-white font-medium text-lg border border-[#e3e6f1] bg-[#1E1E1E] px-3 py-1 rounded-2xl flex items-center gap-2"
                  >
                    {toToken?.logo && (
                      <img
                        src={toToken?.logo}
                        alt={toToken?.symbol}
                        className="w-6 h-6 rounded-full"
                      />
                    )}
                    {toToken?.symbol}
                    <span className="pr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 text-gray-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="white"
                        strokeWidth={4}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </span>
                  </button>
                  {showToDropdown && (
                    <div className="absolute top-full mt-2 -left-6 w-[120%] bg-white border-2 border-[#1E1E1E] rounded-md shadow-lg z-50 max-h-64 overflow-auto">
                      {tokens
                        .filter(
                          (token) =>
                            token?.address === NATIVE_MAS_COIN_ADDRESS ||
                            token?.address === WMAS_TOKEN_ADDRESS
                        )
                        .map((token) => (
                          <div
                            key={token?.symbol}
                            onClick={() => {
                              setToToken(token);
                              setShowToDropdown(false);
                            }}
                            className="flex items-center gap-3 p-3 hover:bg-[#f1f5f9] cursor-pointer border-b border-[#1E1E1E]"
                          >
                            <img
                              src={token?.logo}
                              alt={token?.symbol}
                              className="w-6 h-6 rounded-full"
                            />
                            <div>
                              <span className="font-medium text-black">
                                {token?.symbol}
                              </span>
                              <small className="block text-gray-400">
                                {token?.name}
                              </small>
                            </div>
                          </div>
                        ))}
                    </div>
                  )}
                </div>
              </div>
              {insufficientToBalance && toToken && (
                <p className="text-red-500 text-sm mt-1">
                  Insufficient {toToken?.symbol} balance
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Trading Fees */}
        <div className="w-full max-w-6xl mb-4">
          <p className="font-semibold mb-4 text-xl text-black">
            Select Fee Tier
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {feeOptions.map((fee) => (
              <button
                key={fee.value}
                onClick={() => {
                  setSelectedFee(fee.value);
                  if (connectedAccount && fromToken) {
                    checkPoolExists(
                      fromToken.address,
                      tokens.find((t) => t?.address === WMAS_TOKEN_ADDRESS)!
                        ?.address,
                      Number(fee.value.replace("%", ""))
                    );
                  }
                }}
                className={`p-4 rounded-lg text-center transition-all ${
                  selectedFee === fee.value
                    ? "bg-[#1E1E1E] text-white shadow-lg scale-105"
                    : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                }`}
              >
                <div className="text-lg font-bold">{fee.value}</div>
                <div className="text-sm mt-1">{fee.description}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Create Pool or Connect Wallet button */}
        <div className="flex justify-end w-full max-w-6xl mb-4">
          {!connectedAccount ? (
            <button
              className="w-full border-b-[6px] border-2 border-[#000000] max-w-xs py-3 text-lg font-semibold bg-[#F6C955] text-[#333333] rounded-lg hover:opacity-90 transition-opacity"
              onClick={() => setToggleConnectWalletModal(true)}
            >
              {buttonText}
            </button>
          ) : (
            <button
              className={`w-full border-b-[6px] border-2 border-[#000000] max-w-xs py-3 text-lg font-semibold rounded-lg transition-opacity focus:outline-none ${
                buttonDisabled
                  ? "bg-gray-400 text-white cursor-not-allowed"
                  : "bg-[#F6C955] text-[#333333] hover:opacity-90"
              }`}
              disabled={buttonDisabled}
              onClick={() => {
                if (!connectedAccount) {
                  toast.info("Please connect your wallet.");
                } else if (!buttonDisabled && buttonText === "Create Pool") {
                  handleCreatePool();
                }
              }}
            >
              {buttonText}
            </button>
          )}
        </div>
      </div>
    </>
  );
};

export default CreatePool;
