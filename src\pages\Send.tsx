import { useState, useEffect, useCallback } from "react";
import { useAccountStore } from "@massalabs/react-ui-kit";
import ConnectWalletModal from "../components/ConnectWalletModal";
import { useSelector } from "react-redux";
import { RootState, useAppDispatch } from "../redux/store";
import { addToken, fetchTokens, Token } from "../redux/features/tokensSlice";
import { toast } from "react-toastify";
import {
  Args,
  formatUnits,
  Mas,
  MRC20,
  parseUnits,
  SmartContract,
} from "@massalabs/massa-web3";
import TokenDropdownSearch from "../components/TokenDropdownSearch";
import {
  formatBalance,
  formatStringDecimals,
  waitForExecution,
} from "../lib/utils";

const Send = () => {
  const [toggleConnectWalletModal, setToggleConnectWalletModal] =
    useState(false);
  const { connectedAccount, currentWallet } = useAccountStore();
  const [network, setNetwork] = useState<string | null>(null);
  const dispatch = useAppDispatch();
  const tokens: Token[] = useSelector(
    (state: RootState) => state.tokens.tokens
  );

  useEffect(() => {
    const fetchNetworkInfo = async () => {
      if (connectedAccount && currentWallet) {
        const networkInfo = await currentWallet.networkInfos();
        const networkName = networkInfo?.name ?? null;
        setNetwork(networkName);
      } else {
        setNetwork(null);
      }
    };

    fetchNetworkInfo();

    const handleNetworkChange = async () => {
      await fetchNetworkInfo();
    };

    if (currentWallet) {
      currentWallet.listenNetworkChanges(handleNetworkChange);
    }
  }, [connectedAccount, currentWallet]);

  useEffect(() => {
    dispatch(fetchTokens());
  }, [dispatch]);

  const [selectedToken, setSelectedToken] = useState(tokens[0]);
  const [tokenBalance, setTokenBalance] = useState<number>(0);
  const [amount, setAmount] = useState<string>("");
  const [walletAddress, setWalletAddress] = useState<string>("");
  const handleImportToken = (token: Token) => {
    if (!tokens.some((t) => t.address === token.address)) {
      dispatch(addToken(token));
    }
  };
  const fetchTokenBalance = useCallback(async () => {
    if (!connectedAccount || !selectedToken) {
      setTokenBalance(0);
      return;
    }

    try {
      let balance: number;
      if (selectedToken.is_native) {
        const masBalance = await connectedAccount.balance(false);
        balance = Number(formatUnits(masBalance, selectedToken.decimals));
      } else {
        const tokenContract = new MRC20(
          connectedAccount,
          selectedToken.address
        );
        const rawBalance = await tokenContract.balanceOf(
          connectedAccount.address
        );
        balance = Number(formatUnits(rawBalance, selectedToken.decimals));
      }
      setTokenBalance(balance);
    } catch (error) {
      console.error("Error fetching token balance:", error);
      setTokenBalance(0);
    }
  }, [connectedAccount, selectedToken]);

  useEffect(() => {
    fetchTokenBalance();
  }, [fetchTokenBalance]);

  const handleSend = async () => {
    if (!connectedAccount) {
      setToggleConnectWalletModal(true);
      return;
    }

    if (!amount || !walletAddress) {
      toast.error("Please enter an amount and wallet address.");
      return;
    }

    const loadingToastId = toast.loading("Sending...");
    let operationId: string | null = null;

    try {
      // let operationId: string;
      if (selectedToken.symbol === "MAS") {
        console.log("amount in mas", amount);
        const masAmount = Mas.fromString(amount);
        const operation = await connectedAccount.transfer(
          walletAddress,
          masAmount
        );

        operationId = operation.id;
        // const events = await connectedAccount.getEvents({
        //   callerAddress: connectedAccount.address,
        //   operationId: operationId,
        // });

        // console.log("events to test for snap:", events);

        const [, isSuccess] = await waitForExecution(operation);
        if (isSuccess) {
          toast.update(loadingToastId, {
            render: (
              <div>
                {`Successfully sent ${amount} MAS to ${walletAddress}.`}
                <a
                  href={
                    network?.toUpperCase() === "BUILDNET"
                      ? `https://www.massexplo.com/tx/${operation.id}?network=buildnet`
                      : `https://explorer.massa.net/mainnet/operation/${operation.id}`
                  }
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block mt-2 text-blue-600 hover:underline"
                >
                  View on Explorer
                </a>
              </div>
            ),
            type: "success",
            isLoading: false,
            autoClose: 5000,
          });
          setAmount("");
          setWalletAddress("");
        } else {
          throw new Error("Failed to send MAS.");
        }
      } else {
        const contract = new SmartContract(
          connectedAccount as any,
          selectedToken.address
        );
        // const amountInSmallestUnit = BigInt(
        //   Math.round(Number(amount) * Math.pow(10, selectedToken.decimals))
        // );
        const amountInSmallestUnit = parseUnits(
          formatStringDecimals(amount, selectedToken.decimals),
          selectedToken.decimals
        );

        const args = new Args()
          .addString(walletAddress)
          .addU256(amountInSmallestUnit);

        const operation = await contract.call("transfer", args.serialize(), {
          coins: Mas.fromString("0.01"),
        });

        const [, isSuccess] = await waitForExecution(operation);
        if (isSuccess) {
          toast.update(loadingToastId, {
            render: `Successfully sent ${amount} ${selectedToken.symbol} to ${walletAddress}.`,
            type: "success",
            isLoading: false,
            autoClose: 5000,
          });
        } else {
          throw new Error("Failed to send tokens.");
        }
      }
    } catch (error) {
      console.error("Error sending:", error);

      // Error message with explorer link if available
      const errorMessage = operationId ? (
        <div>
          An error occurred while sending.
          <a
            href={
              network?.toUpperCase() === "BUILDNET"
                ? `https://www.massexplo.com/tx/${operationId}?network=buildnet`
                : `https://explorer.massa.net/mainnet/operation/${operationId}`
            }
            target="_blank"
            rel="noopener noreferrer"
            className="block mt-2 text-blue-600 hover:underline"
          >
            View on Explorer
          </a>
        </div>
      ) : (
        "An error occurred while sending."
      );

      toast.update(loadingToastId, {
        render: errorMessage,
        type: "error",
        isLoading: false,
        autoClose: 5000,
      });
    }
  };

  const isSendDisabled = !connectedAccount || !amount || !walletAddress;

  return (
    <>
      <ConnectWalletModal
        toggleConnectWalletModal={toggleConnectWalletModal}
        setToggleConnectWalletModal={setToggleConnectWalletModal}
      />
      <div className="main-content-wrap flex flex-col justify-between ">
        <div className="main-content">
          <div className="md:container">
            <div className="flex flex-col items-center mt-8 md:mt-16 mb-8">
              <div className="w-full max-w-md bg-[#F6F6F6] rounded-xl border-2 border-[#000000] border-b-[8px] border-r-[6px] p-6">
                <div className="text-3xl font-bold text-black text-center mb-6">
                  Send Tokens
                </div>
                <div className="flex flex-col gap-6">
                  {/* You're Sending Section */}
                  <div className="flex flex-col gap-2">
                    <div className="rounded-lg p-4 border-2 bg-[#E6EBEC] text-[#6e7aaa] relative hover:border-[#3F3F46]">
                      <div className="flex justify-between mb-2">
                        <small className={`font-medium text-black`}>
                          You're sending
                        </small>
                        {connectedAccount && (
                          <small
                            className={`cursor-pointer hover:underline text-black`}
                            onClick={() =>
                              setAmount(formatBalance(tokenBalance))
                            }
                          >
                            Balance: {formatBalance(tokenBalance)}
                          </small>
                        )}
                      </div>
                      <div className="flex items-center gap-4">
                        <TokenDropdownSearch
                          selectedToken={selectedToken}
                          onSelectToken={setSelectedToken}
                          tokens={tokens}
                          onImportToken={handleImportToken}
                        />
                        <div className="flex-1">
                          <input
                            value={amount}
                            onChange={(e) => setAmount(e.target.value)}
                            className="w-full bg-transparent text-right text-xl font-bold placeholder-gray-500 focus:outline-none text-[#6e7aaa]"
                            placeholder="0.0"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* To: Wallet Address Section */}
                  <div className="flex flex-col gap-2">
                    <small className="font-medium text-black">
                      To: Wallet Address
                    </small>
                    <div className="rounded-lg p-4 border-2 bg-[#E6EBEC] text-[#6e7aaa] relative hover:border-[#3F3F46]">
                      <input
                        value={walletAddress}
                        onChange={(e) => setWalletAddress(e.target.value)}
                        className="w-full bg-transparent text-lg placeholder-gray-500 focus:outline-none text-[#6e7aaa]"
                        placeholder="Enter wallet address"
                      />
                    </div>
                  </div>

                  {/* Send Button */}
                  <button
                    onClick={handleSend}
                    disabled={isSendDisabled}
                    className={`w-full py-3 text-lg font-semibold rounded-xl transition-opacity ${
                      isSendDisabled
                        ? "bg-gray-400 text-white cursor-not-allowed"
                        : "bg-[#1E1E1E] text-[#FFFFFF] hover:opacity-90"
                    }`}
                  >
                    {connectedAccount ? "Send" : "Connect Wallet"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Send;
