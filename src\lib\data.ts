interface NavItem {
  label: string;
  path?: string;
  subItems?: NavItem[];
  soon?: boolean;
  isNew?: boolean;
}

export const navItems: NavItem[] = [
  {
    label: "Trade",
    path: "/",
    subItems: [
      { label: "Swap", path: "/" },
      { label: "Perps", path: "/perps", soon: true },
      { label: "Options", path: "/options", soon: true },
      { label: "Send", path: "/send" },
      { label: "Buy Crypto", path: "/buy" },
      { label: "Bridge", path: "/bridge" },
    ],
  },
  {
    label: "Liquidity",
    path: "/liquidity",
  },
  {
    label: "Portfolio",
    path: "/portfolio",
  },
  {
    label: "Create",
    subItems: [
      { label: "Create token", path: "/create-token" },
      // { label: "Create NFT Collection", path: "/create-nft-collection" },
      { label: "Create Pool", path: "/create-pool" },
    ],
  },
  // {
  //   label: "Play",
  //   subItems: [
  //     { label: "Lottery", path: "/lottery", soon: true },
  //     { label: "Prediction", path: "/prediction", soon: true },
  //     { label: "Eagleverse", path: "/eagleverse" },
  //     { label: "Evobots", path: "/evobots", isNew: true },
  //   ],
  // },
  {
    label: "More",
    subItems: [
      { label: "About Us", path: "/docs" },
      { label: "Leaderboard", path: "/points", soon: true },
      { label: "Eagle Assistant", path: "/eagle-assistant", soon: true },

      { label: "Delegate", path: "/delegate", soon: true },
      { label: "Developers", path: "/devdocs" },
      { label: "Blog", path: "/blog", soon: true },
      { label: "Disclaimer", path: "/disclaimer" },
    ],
  },
];

export const blogContent1 = `
Welcome! This tutorial guides you through building smart contracts on the Massa blockchain using practical examples drawn directly from the [EagleFi DEX project](https://github.com/NaDasai/eagle-finance). We'll cover essential concepts, starting with the basics and progressing to more advanced techniques.

**Target Audience:** Developers looking to learn Massa smart contract development using AssemblyScript. Familiarity with basic programming concepts is assumed.

**Source Code:** All examples are simplified snippets from the full EagleFi project, available on [GitHub](https://github.com/NaDasai/eagle-finance).

## Table of Contents

**Beginner**
1.  [Setting Up Your Massa Project](#1-setting-up-your-massa-project)
2.  [Handling Function Inputs](#2-handling-function-inputs)
3.  [Accessing Basic Contract & Transaction Context](#3-accessing-basic-contract--transaction-context)
4.  [Basic State Management (Storage)](#4-basic-state-management-storage)

**Intermediate**

5.  [Utility: Wrapping Native MAS to WMAS](#5-utility-wrapping-native-mas-to-wmas)
6.  [Working with Native MAS Coins](#6-working-with-native-mas-coins)
7.  [Creating Your Custom MRC20 Token](#7-creating-your-custom-mrc20-token)
8.  [Interacting with MRC20 Tokens](#8-interacting-with-mrc20-tokens)

**Advanced**

9. [Deploying Contracts from Other Contracts](#9-deploying-contracts-from-other-contracts)
10. [Working with Complex Data: Serializable Objects](#10-working-with-complex-data-serializable-objects)
11. [Best Practice: Refunding Storage Costs](#11-best-practice-refunding-storage-costs)
12. [Secure Ownership with Two-Step Transfer](#12-secure-ownership-with-two-step-transfer)
13. [Security Pattern: Reentrancy Guard](#13-security-pattern-reentrancy-guard)

**Conclusion**

14. [Conclusion](#14-conclusion)


---

# Beginner

This section covers the absolute basics: setting up your development environment, understanding how functions receive data, getting essential context information, and storing simple data.

## 1. Setting Up Your Massa Project

First, let's set up the basic structure for a Massa smart contract project using the official initializer.

1.  **Initialize a new project:** This command creates a new directory with the necessary boilerplate code and configuration.
    \`\`\`bash
    npx @massalabs/sc-project-initializer init my-first-sc
    cd my-first-sc
    \`\`\`
2.  **Install dependencies:** This installs the required Node.js packages, including the Massa SDK and AssemblyScript compiler tools.
    \`\`\`bash
    npm install
    \`\`\`
3.  **Configure environment variables:** Create a \`.env\` file from the provided example. This file will store sensitive information like your private key.
    \`\`\`bash
    cp .env.example .env
    \`\`\`
4.  **Add your private key:** Edit the \`.env\` file and insert your Massa account private key. This is needed for deploying contracts and sending transactions.
    \`\`\`bash
    PRIVATE_KEY=your_private_key
    \`\`\`
    **Important:** Never commit your \`.env\` file containing private keys to version control (like Git). Ensure \`.env\` is listed in your \`.gitignore\` file.

## 2. Handling Function Inputs

Massa smart contract functions receive their arguments encoded into a single byte array (\`StaticArray<u8>\`). To work with these arguments within your function, you need to deserialize them using the \`Args\` class provided by the SDK.

Here's how you can extract different data types from the input byte array, demonstrated in the \`createNewToken\` function from \`tokenDeployer.ts\`:
\`\`\`typescript
import { Args } from "@massalabs/massa-as-sdk";
const args = new Args(binaryArgs);

const tokenName = args.nextString().expect('Invalid token name');
const tokenSymbol = args.nextString().expect('Invalid token symbol');
const decimals = args.nextU8().expect('Invalid decimals');
const totalSupply = args.nextU256().expect('Invalid total supply');
\`\`\`
The \`next<Type>()\` methods deserialize the next part of the byte array according to the expected type. The \`.expect()\` method provides a convenient way to handle potential errors during deserialization, reverting the transaction with a helpful message if the data is missing or malformed.

## 3. Accessing Basic Contract & Transaction Context

During execution, a smart contract often needs information about its environment. The \`Context\` object provides some of this essential information.

*   **Get Caller Address:** Identify the address that initiated the current function call. This is crucial for access control and tracking interactions.
    \`\`\`typescript
    import { Context } from "@massalabs/massa-as-sdk";

    const callerAddress = Context.caller();
    \`\`\`

*   **Get Current Contract Address:** Obtain the address of the smart contract currently being executed. Useful when the contract needs to reference itself.
    \`\`\`typescript
    import { Context } from "@massalabs/massa-as-sdk";

    const contractAddress = Context.callee();
    \`\`\`

*   **Get Transferred Coins (in current call):** You can determine how many MAS coins were sent *along with the current function call* using \`Context.transferredCoins()\`. This is essential for functions that require payment. Example from \`tokenDeployer.ts\` (L97):
    \`\`\`typescript
    import { Context } from "@massalabs/massa-as-sdk";

    // Get the coins transferred to the smart contract
    const sent = Context.transferredCoins();
    \`\`\`

## 4. Basic State Management (Storage)

Smart contracts need to persist data between transactions. Massa provides a key-value storage system where both keys and values are stored as byte arrays (\`StaticArray<u8>\`).

*   **Defining and Setting State Variables:** You typically define keys as constants (often converting strings to bytes) and use \`Storage.set()\` to store data.

    This example from the \`registry\` contract shows how to define a key and implement a function to set the address of a swap router:
    \`\`\`typescript
    import { Storage, assertIsSmartContract} from "@massalabs/massa-as-sdk";

    // Storage Key containning the address of the swap Router contract to be used on all the pools
    export const swapRouterAddress = stringToBytes('swapRouterAddress');


    export function setSwapRouterAddress(binaryArgs: StaticArray<u8>): void {
       const args = new Args(binaryArgs);

      // Get the swapRouterAddress input
      const swapRouterAddressInput = args
        .nextString()
        .expect('SwapRouterAddress is missing or invalid');

      // Assert that the swapRouterAddress is a smart contract
      assertIsSmartContract(swapRouterAddressInput);

      // Set the swapRouterAddress Storage to the input value
      Storage.set(swapRouterAddress, stringToBytes(swapRouterAddressInput));
    }
    \`\`\`

*   **Retrieving State Variables:** Use \`Storage.get()\` with the appropriate key to retrieve stored data. Remember that the data is returned as bytes and may need conversion. It's also vital to check if the data actually exists before using it.

    This example shows how to retrieve the previously stored swap router address:
    \`\`\`typescript
    import { Storage } from "@massalabs/massa-as-sdk";

    export function getSwapRouterAddress(): StaticArray<u8> {  // Get the swapRouterAddress
      // Retrieve the swapRouterAddress from the storage
      const swapRouterAddressStored = Storage.get(swapRouterAddress);

      // Assert that the swapRouterAddress is set (Not empty storage)
      assert(swapRouterAddressStored.length > 0, 'Swap Router Address is not set');

      return swapRouterAddressStored;
    }
    \`\`\`

---

# Intermediate

This section builds upon the basics, covering interactions with Massa's native coin, creating and interacting with standard MRC20 tokens, and understanding allowances.

## 5. Utility: Wrapping Native MAS to WMAS

WMAS (Wrapped MAS) is an MRC20 token that represents native MAS on a 1:1 basis. Wrapping allows native MAS to be used in contexts requiring MRC20 tokens, such as DEX liquidity pools.

*   **The \`wrapMasToWMAS\` Helper:** This function takes an amount of MAS to wrap and the address of the WMAS contract. It checks if sufficient native MAS was sent with the call (to cover the wrap amount plus estimated storage costs for minting WMAS) and then calls the \`deposit\` function on the WMAS contract.

    Implementation from \`utils/index.ts\`:
    \`\`\`typescript
    /**
     * Wraps a specified amount of MAS coins into WMAS tokens.
     *
     * @param amount - The amount of MAS coins to be wrapped into WMAS tokens.
     * @param wmasAddress - The address of the WMAS token contract.
     * @throws Will throw an error if the transferred MAS coins are insufficient.
     */
    export function wrapMasToWMAS(amount: u256, wmasAddress: Address): void {
      // Get the transferred coins from the operation
      const transferredCoins = Context.transferredCoins();

      // Get the wmas contract instance
      const wmasToken = new IWMAS(wmasAddress);

      const mintStorageCost = u256.fromU64(
        _computeMintStorageCost(Context.callee()),
      );

      const amountToWrap = SafeMath256.add(amount, mintStorageCost);

      // Ensure that transferred coins are greater than or equal to the amount to wrap
      assert(
        u256.fromU64(transferredCoins) >= amountToWrap,
        'INSUFFICIENT MAS COINS TRANSFERRED',
      );

      // Wrap MAS coins into WMAS
      wmasToken.deposit(amountToWrap.toU64());

      // Generate an event to indicate that MAS coins have been wrapped into WMAS
      generateEvent(\`WRAP_MAS: \${amount.toString()} of MAS wrapped into WMAS\`);
    }
    \`\`\`

*   **Usage:** In scenarios where a function receives native MAS but needs to interact with a system using WMAS (like swapping MAS for another token), this helper can be called first.

    Example usage within the \`_swap\` function in \`swapRouter.ts\`:
    \`\`\`typescript
    function _swap(
      swapPath: SwapPath,
      callerAddress: Address,
      contractAddress: Address,
      toAddress: Address,
      coinsOnEachSwap: u64,
    ): u256 {
      const poolAddress = swapPath.poolAddress;
      const tokenInAddress = swapPath.tokenInAddress.toString();
      const tokenOutAddress = swapPath.tokenOutAddress.toString();
      const amountIn = swapPath.amountIn;

      // ...


      // Wrap mas before swap and transfer wmas
      const registryContractAddressStored = bytesToString(
        Storage.get(registryContractAddress),
      );

      // Get the wmas token address
      const wmasTokenAddressStored = new Address(
        new IRegistery(
          new Address(registryContractAddressStored),
        ).getWmasTokenAddress(),
      );

      // Wrap Mas to WMAS (assuming amountIn is the native MAS sent)
      wrapMasToWMAS(amountIn, wmasTokenAddressStored);

      // ... subsequent logic uses the WMAS minted to this contract ...

    }
    \`\`\`

## 6. Working with Native MAS Coins

Contracts can interact with Massa's native coin (MAS) in more ways than just checking the amount sent in a call.

*   **Get Contract's Total MAS Balance:** Retrieve the current total balance of native MAS coins held by the smart contract itself.
    \`\`\`typescript
    import { balance } from "@massalabs/massa-as-sdk";

    const SCBalance = balance();
    \`\`\`

*   **Transfer Coins:** Contracts can send MAS coins from their own balance to another address using \`transferCoins()\`. Example from \`utils/index.ts\`:
    \`\`\`typescript
    import { transferCoins } from "@massalabs/massa-as-sdk";

    function _transferRemaining(to: Address, value: u64): void {
      // Transfer coins to the specified address
      transferCoins(to, value);
    }
    \`\`\`

## 7. Creating Your Custom MRC20 Token

Massa uses the MRC20 standard for fungible tokens (similar to ERC20). You can create your own custom token by extending the standard implementation provided by \`@massalabs/sc-standards\`.

1.  **Install the Standards Package:**
    \`\`\`bash
    npm install @massalabs/sc-standards
    \`\`\`
2.  **Build Your Token Contract:** Create a new AssemblyScript file (e.g., \`token.ts\`).
    *   **Export Standard Functions:** Re-export the default MRC20 functions you don't intend to modify. This avoids rewriting standard logic. Example from \`token.ts\` (L396):
        \`\`\`typescript
        export {
          VERSION,
          version,
          name,
          symbol,
          decimals,
          totalSupply,
          balanceOf,
          allowance,
          increaseAllowance,
          decreaseAllowance,
        } from '@massalabs/sc-standards/assembly/contracts/MRC20/MRC20';
        \`\`\`
    *   **Import Standard Keys:** Import the predefined storage keys for standard MRC20 properties if you need to interact with them directly (e.g., in your constructor).
        \`\`\`typescript
        import {
          NAME_KEY,
          DECIMALS_KEY,
          SYMBOL_KEY,
          TOTAL_SUPPLY_KEY,
        } from '@massalabs/sc-standards/assembly/contracts/MRC20/MRC20';
        \`\`\`
    *   **Add Custom Features:** To add fields like description, image, or website:
        1.  **Define new storage keys:**
            \`\`\`typescript
            export const TOKEN_DESCRIPTION = stringToBytes('TOKEN_DESCRIPTION');
            export const TOKEN_IMAGE = stringToBytes('TOKEN_IMAGE');
            export const TOKEN_WEBSITE = stringToBytes('TOKEN_WEBSITE');
            \`\`\`
        2.  **Set values in the constructor:** Read these potentially optional values from the arguments and store them using \`Storage.set()\`. Use \`unwrapOrDefault()\` for optional arguments.
            \`\`\`typescript
            export function constructor(binaryArgs: StaticArray<u8>): void {
              assert(isDeployingContract());

              const args = new Args(binaryArgs);

              // Admin arg passed by the token deployer to specify the owner of the token
              const admin = args.nextString().expect('Invalid admin');
              const tokenName = args.nextString().expect('Invalid token name');
              const tokenSymbol = args.nextString().expect('Invalid token symbol');
              const decimals = args.nextU8().expect('Invalid decimals');
              const totalSupply = args.nextU256().expect('Invalid total supply');
              // optional parameter
              const image = args.nextString().unwrapOrDefault();
              // optional Parameter
              const website = args.nextString().unwrapOrDefault();
              // optional parameter
              const description = args.nextString().unwrapOrDefault();

              // ...

              // Set the token name, symbol, decimals, total supply, image, website and description in the storage
              Storage.set(NAME_KEY, stringToBytes(tokenName));
              Storage.set(SYMBOL_KEY, stringToBytes(tokenSymbol));
              Storage.set(DECIMALS_KEY, [decimals]);
              Storage.set(TOTAL_SUPPLY_KEY, u256ToBytes(totalSupply));
              Storage.set(TOKEN_IMAGE, stringToBytes(image));
              Storage.set(TOKEN_DESCRIPTION, stringToBytes(description));
              Storage.set(TOKEN_WEBSITE, stringToBytes(website));

              // ...
            }
            \`\`\`
        3.  **Implement getter functions:** Provide functions to retrieve the custom data from storage.
            \`\`\`typescript
            export function image(_: StaticArray<u8>): StaticArray<u8> {
              return Storage.get(TOKEN_IMAGE);
            }

            export function website(_: StaticArray<u8>): StaticArray<u8> {
              return Storage.get(TOKEN_WEBSITE);
            }

            export function description(_: StaticArray<u8>): StaticArray<u8> {
              return Storage.get(TOKEN_DESCRIPTION);
            }
            \`\`\`

## 8. Interacting with MRC20 Tokens

To interact with existing MRC20 tokens (standard or custom) from within your smart contract, you can use wrappers or interfaces.

*   **Using \`MRC20Wrapper\` or Custom Interfaces:** The \`@massalabs/sc-standards\` package provides \`MRC20Wrapper\`. For tokens with custom functions (like the one created in Section 7), it's best to create a custom interface that *extends* \`MRC20Wrapper\`, adding methods for your custom functions.

    Example \`IMRC20\` interface extending the standard wrapper:
    \`\`\`typescript
    import { MRC20Wrapper } from "@massalabs/sc-standards/assembly/contracts/MRC20/wrapper";

    export class IMRC20 extends MRC20Wrapper implements Serializable {
       constructor(origin: Address = new Address()) {
        super(origin);
      }

      // Add you custom functions here, like image(), website(), description()
     image(): string {
        return bytesToString(call(this._origin, 'image', NoArg, 0));
      }

      website(): string {
        return bytesToString(call(this._origin, 'website', NoArg, 0));
      }

      description(): string {
        return bytesToString(call(this._origin, 'description', new Args(), 0));
      }

      // ...
    }
    \`\`\`

*   **Transferring Tokens (\`transferFrom\`)**: To allow your contract (e.g., a DEX router) to spend a user's tokens, the user must first call \`approve\` on the token contract, granting your contract an allowance. Your contract then uses \`transferFrom\` to execute the transfer. It's crucial to check the user's balance and allowance beforehand. Massa standards also provide helpers like \`getBalanceEntryCost\` to estimate the MAS cost associated with storage changes during the transfer.

    Example from \`swapRouter.ts\` showing checks and \`transferFrom\`:
    \`\`\`typescript
    import { getBalanceEntryCost } from '@massalabs/sc-standards/assembly/contracts/MRC20/MRC20-external';
    import { IMRC20 } from '../interfaces/IMRC20';

    function _swap(
      swapPath: SwapPath,
      callerAddress: Address,
      contractAddress: Address,
      toAddress: Address,
      coinsOnEachSwap: u64,
    ): u256 {
      // ...

      const tokenIn = new IMRC20(swapPath.tokenInAddress);

      // ...

      // Check for balance
      const tokenInBalance = tokenIn.balanceOf(callerAddress);

      assert(tokenInBalance >= amountIn, 'INSUFFICIENT_TOKEN_IN_BALANCE');

      const tokenInAllownace = tokenIn.allowance(
        callerAddress,
        contractAddress,
      );

      // Check for allowance
      assert(tokenInAllownace >= amountIn, 'INSUFFICIENT_TOKEN_IN_ALLOWANCE');

      // Transfer amountIn from user to this contract
      tokenIn.transferFrom(
        callerAddress,
            contractAddress,
            amountIn,
            getBalanceEntryCost(tokenInAddress, contractAddress.toString()),
          );

      // ...

    }
    \`\`\`

---

# Advanced

This section delves into more complex topics like deploying contracts from contracts, handling complex data structures, and implementing important security and efficiency patterns.

## 9. Deploying Contracts from Other Contracts

A powerful feature is the ability for one smart contract to deploy another. This is used in EagleFi's \`Registry\` contract to deploy new liquidity pool contracts.

1.  **Prerequisites:**
    *   Install the \`@massalabs/as-transformer\` package (usually as a dev dependency).
    *   Define an interface (e.g., \`interfaces/basicPool.ts\`) for the contract you intend to deploy. This helps structure the interaction.
2.  **Deployment Logic:** Use the \`createSC\` function with the target contract's bytecode (\`.wasm\` file) to deploy a new instance. Then, interact with the new contract via its interface to call initialization functions.

    Here's an example from the \`Registry\` contract's \`_createNewPool\` function (L618) deploying a \`basicPool\` contract:
    \`\`\`typescript
    import { createSC, fileToByteArray } from "@massalabs/massa-as-sdk";

    function _createNewPool(
      aTokenAddress: string,
      bTokenAddress: string,
      inputFeeRate: u64,
    ): CreateNewPoolData {
      // ...

      //  Deploy the pool contract
      const poolByteCode: StaticArray<u8> = fileToByteArray('build/basicPool.wasm');
      const poolAddress = createSC(poolByteCode);

      //  Init the pool contract
      const poolContract = new IBasicPool(poolAddress);

      poolContract.init(
        aTokenAddress,
        bTokenAddress,
        inputFeeRate,
        feeShareProtocolStored,
        flashLoanFeeStored,
        Context.callee().toString(), // registry address
      );

      // ...
    }
    \`\`\`

## 10. Working with Complex Data: Serializable Objects

Instead of passing numerous individual arguments to functions, Massa allows you to define custom classes that implement the \`Serializable\` interface. This enables you to bundle related data and pass it as a single, complex object.

*   **Defining a Serializable Class:** You need to implement \`serialize\` (object to bytes) and \`deserialize\` (bytes to object) methods.

    Example \`SwapPath\` class from \`structs/swapPath.ts\`:
    \`\`\`typescript
    import { Args, Result, Serializable } from '@massalabs/as-types';
    import { Address } from '@massalabs/massa-as-sdk';
    import { u256 } from 'as-bignum/assembly';

    export class SwapPath implements Serializable {
      constructor(
        public poolAddress: Address = new Address(),
        public tokenInAddress: Address = new Address(),
        public tokenOutAddress: Address = new Address(),
        public receiverAddress: Address = new Address(),
        public amountIn: u256 = u256.Zero,
        public minAmountOut: u256 = u256.Zero,
        public isTransferFrom: bool = false,
      ) {}

      serialize(): StaticArray<u8> {
        return new Args()
          .add(this.poolAddress)
          .add(this.tokenInAddress)
          .add(this.tokenOutAddress)
          .add(this.receiverAddress)
          .add(this.amountIn)
          .add(this.minAmountOut)
          .add(this.isTransferFrom)
          .serialize();
      }

      deserialize(data: StaticArray<u8>, offset: i32): Result<i32> {
        const args = new Args(data, offset);

        this.poolAddress = new Address(args.nextString().expect('Invalid address'));
        this.tokenInAddress = new Address(
          args.nextString().expect('Invalid address'),
        );
        this.tokenOutAddress = new Address(
          args.nextString().expect('Invalid address'),
        );
        this.receiverAddress = new Address(
          args.nextString().expect('Invalid address'),
        );
        this.amountIn = args.nextU256().expect('Invalid amount in');
        this.minAmountOut = args.nextU256().expect('Invalid min amount out');
        this.isTransferFrom = args.nextBool().expect('Invalid isTransferFrom');

        return new Result(args.offset);
      }

      toString(): string {
        return (
          \`Pool Address: \${this.poolAddress.toString()}\` +
          \`Token In Address: \${this.tokenInAddress.toString()}\` +
          \`Token Out Address: \${this.tokenOutAddress.toString()}\` +
          \`Receiver Address: \${this.receiverAddress.toString()}\` +
          \`Amount In: \${this.amountIn.toString()}\` +
          \`Min Amount Out: \${this.minAmountOut.toString()}\` +
          \`Is Transfer From: \${this.isTransferFrom}\`
        );
      }
    }

    \`\`\`

*   **Using Serializable Objects in Functions:** You can deserialize these objects (or arrays of them) from the \`binaryArgs\` using methods like \`nextSerializableObjectArray\`.

    Example from \`swapRouter.ts\` deserializing an array of \`SwapPath\` objects:
    \`\`\`typescript
    import { SwapPath } from '../structs/swapPath';
    import { Args } from '@massalabs/as-types';

    export function swap(binaryArgs: StaticArray<u8>): void {
      // ...

      const args = new Args(binaryArgs);

      // Read the swap Path array args
      let swapPathArray = args
        .nextSerializableObjectArray<SwapPath>()
        .expect('Invalid swap path array');

      // ...
    }
    \`\`\`

## 11. Best Practice: Refunding Storage Costs

Users interacting with smart contracts often send more MAS coins than strictly necessary to cover potential storage costs incurred during execution. It's essential to implement a mechanism to refund any unused MAS back to the caller.

*   **The \`transferRemaining\` Function:** This utility calculates the net MAS spent or gained by the contract during an operation (by comparing balances before and after) and refunds the difference relative to the amount initially sent by the user.

    Implementation from \`utils/index.ts\`:
    \`\`\`typescript
    /**
     * @notice Function to transfer remaining Massa coins to a recipient at the end of a call
     * @param balanceInit Initial balance of the SC (transferred coins + balance of the SC)
     * @param balanceFinal Balance of the SC at the end of the call
     * @param sent Number of coins sent to the SC
     * @param to Caller of the function to transfer the remaining coins to
     */
    export function transferRemaining(
      balanceInit: u64,
      balanceFinal: u64,
      sent: u64,
      to: Address,
    ): void {
      if (balanceInit >= balanceFinal) {
        // Some operation might spend Massa by creating new storage space
        const spent = SafeMath.sub(balanceInit, balanceFinal);
        generateEvent(\`Spent \${spent} coins\`);
        assert(spent <= sent, 'SPENT_MORE_COINS_THAN_SENT');
        if (spent < sent) {
          // SafeMath not needed as spent is always less than sent
          const remaining: u64 = sent - spent;
          _transferRemaining(to, remaining);
        }
      } else {
        // Some operation might unlock Massa by deleting storage space
        const received = SafeMath.sub(balanceFinal, balanceInit);
        const totalToSend: u64 = SafeMath.add(sent, received);
        _transferRemaining(to, totalToSend);
      }
    }

    function _transferRemaining(to: Address, value: u64): void {
      transferCoins(to, value);
    }
    \`\`\`

*   **Usage:** To use this, capture the contract balance and sent coins at the start of your function, execute the core logic, capture the balance again at the end, and finally call \`transferRemaining\`.

    Example from \`basicPool.ts\` \`syncReserves\` function:
    \`\`\`typescript
    import {
      transferRemaining
    } from '../utils';
    import {
      Context,
      balance,
    } from '@massalabs/massa-as-sdk';

    export function syncReserves(): void {
      // ...
      const SCBalance = balance(); // Balance before logic
      const sent = Context.transferredCoins(); // Coins sent with call
      // ...
      // --- Core function logic ---
      // ...

      // Transfer remaining coins to the caller
      transferRemaining(SCBalance, balance(), sent, Context.caller()); // Pass initial balance, final balance, sent amount, and caller
    }
    \`\`\`

## 12. Secure Ownership with Two-Step Transfer

Standard single-step ownership transfers in smart contracts carry the risk of irrecoverably losing control if the new owner's address is entered incorrectly. EagleFi implements a robust two-step ownership transfer mechanism. This pattern significantly enhances security by requiring confirmation from the proposed new owner before the transfer is finalized.

**How it Works:**
 1.  The current owner initiates a transfer, designating a \`pendingOwner\`.
 2.  The designated \`pendingOwner\` must actively call an \`acceptOwnership\` function to claim ownership.
 
Here is an example of how this pattern is implemented in EagleFi. you can find the full implementation in the \`utils/ownership.ts\`:

 \`\`\`typescript	
 // Storage key for the owner
 const OWNER_KEY = 'OWNER';
 // Storage key for the pending owner
 const pendingOwner = 'PENDING_OWNER';
 
 
 /**
  * Sets the owner of the contract.
  *
  * @param owner - The address to set as owner.
  */
 export function _setOwner(owner: Address): void {
   Storage.set(OWNER_KEY, owner.toString());
 }
 
 /**
  * Returns the owner address of the contract.
  *
  * @returns The owner address.
  */
 export function _ownerAddress(): Address {
   return new Address(Storage.get(OWNER_KEY));
 }
 
 
 /**
  * Transfers the ownership of the contract to a new owner.
  *
  * @param binaryArgs - The binary arguments containing the new owner address.
  */
 export function transferOwnership(binaryArgs: StaticArray<u8>): void {
   const args = new Args(binaryArgs);
   const newOwner = args.nextString().expect('Invalid new owner');
 
   // Ensure that the caller is the owner
   _onlyOwner();
 
   // Ensure that the new owner address is valid
   assert(validateAddress(newOwner), 'INVALID_OWNER_ADDRESS');
 
   // Set a new pending owner
   Storage.set(pendingOwner, newOwner);
 
   // Emit an event
   generateEvent(ownershipTransferStartedEvent(_ownerAddress(), new Address(newOwner)));
 }
 
 /**
  * Accepts the ownership transfer of the contract.
  */
 export function acceptOwnership(): void {
   const caller = Context.caller();
   const storedPendingOwner = Storage.get(pendingOwner);
 
   // Ensure that the caller is the pending owner
   assert(caller.toString() === storedPendingOwner, 'CALLER_IS_NOT_PENDING_OWNER');
 
   // Set the new owner
   Storage.set(OWNER_KEY, caller.toString());
 
   // Delete the pending owner
   Storage.del(pendingOwner);
 
   // Emit an event
   generateEvent(ownershipTransferAcceptedEvent(_ownerAddress(), caller));
 }
 
 // Example Usage in Constructor:
 export function constructor(binaryArgs: StaticArray<u8>): void {
   // Set the deployer as the initial owner
   _setOwner(Context.caller());
 
   // Other initialization code...
 }
\`\`\`

## 13. Security Pattern: Reentrancy Guard

Reentrancy attacks can occur when a contract makes an external call to another (potentially malicious) contract, which then immediately calls back into the original contract before the first call has finished executing. Massa does not inherently prevent this. You can implement a reentrancy guard using a simple locking mechanism (status flag in storage).

*   **The \`ReentrancyGuard\` Class:** This class provides functions to initialize the guard, lock (\`nonReentrant\`), and unlock (\`endNonReentrant\`) the contract state.

    Implementation inspired by OpenZeppelin, from \`lib/ReentrancyGuard.ts\`:
    \`\`\`typescript
    import { Storage } from '@massalabs/massa-as-sdk';
    import { byteToU8, stringToBytes, u8toByte } from '@massalabs/as-types';

    export const STATUS = stringToBytes('STATUS');

    const _NOT_ENTERED: u8 = 1;
    const _ENTERED: u8 = 2;

    /// @title Reentrancy Guard
    /// @notice Contract module that helps prevent reentrant calls to a function
    export class ReentrancyGuard {
      static __ReentrancyGuard_init(): void {
        assert(!Storage.has(STATUS), 'ReentrancyGuard already initialized');

        Storage.set(STATUS, u8toByte(_NOT_ENTERED));
      }

      /// @notice Prevents a contract from calling itself, directly or indirectly.
      /// Calling a \`nonReentrant\` function from another \`nonReentrant\`
      /// function is not supported. It is possible to prevent this from happening
      /// by making the \`nonReentrant\` function external, and making it call a
      /// \`private\` function that does the actual work
      static nonReentrant(): void {
        // On the first call to nonReentrant, _notEntered will be true

        assert(
          byteToU8(Storage.get(STATUS)) == _NOT_ENTERED,
          'ReentrancyGuard: calling nonReentrant while already in a call to nonReentrant',
        );

        // Any calls to nonReentrant after this point will fail
        Storage.set(STATUS, u8toByte(_ENTERED));
      }

      static endNonReentrant(): void {
        Storage.set(STATUS, u8toByte(_NOT_ENTERED));
      }
    }
    \`\`\`

*   **Usage:**
    1.  **Initialize:** Call \`ReentrancyGuard.__ReentrancyGuard_init()\` once in your contract's constructor.
        \`\`\`typescript
        import { ReentrancyGuard } from '../lib/ReentrancyGuard';

        export function constructor(binaryArgs: StaticArray<u8>): void {
           // This line is important. It ensures that this function can't be called in the future.
          // If you remove this check, someone could call your constructor function and reset your smart contract.
          assert(Context.isDeployingContract());

          // ...

          // Initialize the reentrancy guard
          ReentrancyGuard.__ReentrancyGuard_init();
        }
        \`\`\`
    2.  **Protect Functions:** Wrap the logic of functions susceptible to reentrancy by calling \`nonReentrant()\` at the beginning and \`endNonReentrant()\` at the end.

        Example from \`registry.ts\` \`setWmasTokenAddress\` function:
        \`\`\`typescript
        export function setWmasTokenAddress(binaryArgs: StaticArray<u8>): void {
          // start reentrancy guard
          ReentrancyGuard.nonReentrant(); // Lock

          // Only owner of registery can set wmas token address
          onlyOwner();

          const args = new Args(binaryArgs);

          const wmasTokenAddressInput = args
            .nextString()
            .expect('WmasTokenAddress is missing or invalid');

          // Get the current balance of the smart contract
          const SCBalance = balance();
          // Get the coins transferred to the smart contract
          const sent = Context.transferredCoins();

          // Ensure taht the wmasTokenAddress is a smart contract address
          assertIsSmartContract(wmasTokenAddressInput);

          // Store wmasTokenAddress
          Storage.set(wmasTokenAddress, stringToBytes(wmasTokenAddressInput));

          // Emit an event
          generateEvent(
            createEvent('UPDATE_WMAS_TOKEN_ADDRESS', [
              Context.callee().toString(), // Smart contract address
              Context.caller().toString(), // Caller address
              wmasTokenAddressInput, // New wmas token address
            ]),
          );

          // Transfer the remaining coins back to the caller
          transferRemaining(SCBalance, balance(), sent, Context.caller());

          // End reentrancy guard
          ReentrancyGuard.endNonReentrant(); // Unlock (Crucial!)
        }
        \`\`\`

---

# Conclusion

## 14. Conclusion

This tutorial has walked you through key aspects of Massa smart contract development using practical examples from the EagleFi DEX, progressing from beginner to advanced topics. You've seen how to set up a project, handle inputs and state, interact with native MAS and MRC20 tokens, deploy contracts programmatically, use serializable objects, and implement important patterns like gas refunding and reentrancy protection.

The best way to solidify your understanding is to dive into the [EagleFi codebase](https://github.com/NaDasai/eagle-finance), experiment with these examples, and start building your own applications on Massa. Happy coding!
    `;
export const blogContent2 = `
# EagleFi: Enhancing u256 capabilities for the Massa Ecosystem

The development of EagleFi, an automated market maker (AMM) DEX on the Massa blockchain, highlighted a need for more robust tooling and examples around \`u256\` arithmetic, which is fundamental for DeFi applications. While Massa provides the \`u256\` type via \`as-bignum\`, practical implementations of safe arithmetic and essential mathematical functions like square root were less prevalent in readily available examples within the ecosystem during our development.

EagleFi addressed this by implementing and refining several \`u256\` focused utilities. We believe sharing these solutions can significantly benefit other developers building complex financial applications on Massa. This document outlines key \`u256\` libraries and patterns derived from the [EagleFi codebase](https://github.com/NaDasai/eagle-finance), which other projects can adopt or learn from.

## Table of Contents

1.  [SafeMath Library for u256 Arithmetic](#1-safemath-library-for-u256-arithmetic)
2.  [Integer Square Root (sqrt) for u256](#2-integer-square-root-sqrt-for-u256)
3.  [Reference Implementation: Practical SafeMath Usage](#3-reference-implementation-practical-safemath-usage)
4.  [Reference Implementation: u256 Support in Liquidity Management](#4-reference-implementation-u256-support-in-liquidity-management)
5.  [Conclusion](#5-conclusion)

---

## 1. SafeMath Library for u256 Arithmetic

**Problem:** Financial calculations in DeFi often involve very large numbers (u256). Standard integer arithmetic lacks protection against overflows and underflows, which can lead to critical vulnerabilities and incorrect calculations.

**Contribution:** EagleFi provides \`SafeMath256\`, a library offering checked arithmetic operations (\`add\`, \`sub\`, \`mul\`, \`div\`, \`mod\`) specifically for \`u256\` values. Any operation that would result in an overflow or underflow will cause the transaction to revert, ensuring calculation integrity.

**Benefits for the Ecosystem:** Offers a fundamental building block for secure DeFi development on Massa, preventing common arithmetic vulnerabilities when dealing with large token amounts or complex financial logic involving \`u256\`.

**Implementation Snippets:**

\`\`\`typescript
export class SafeMath256 {
  /**
   * Returns the addition of two unsigned integers,
   * reverting on overflow.
   */
  static add(a: u256, b: u256): u256 {
    const c = u256.add(a, b);
    assert(c >= a, 'SafeMath: addition overflow');
    return c;
  }

  /**
   * Returns the integer division of two unsigned integers.
   * Reverts with custom message on division by zero.
   */
  static sub(a: u256, b: u256): u256 {
    assert(b <= a, 'SafeMath256: substraction overflow');
    const c = u256.sub(a, b);
    return c;
  }

  /**
   * Returns the multiplication of two unsigned integers,
   * reverting on overflow.
   */
  static mul(a: u256, b: u256): u256 {
    if (a.isZero()) {
      return u256.Zero;
    }

    const c = u256.mul(a, b);
    assert(u256.eq(u256.div(c, a), b), 'SafeMath: multiplication overflow');
    return c;
  }

  /**
   * Returns the integer division of two unsigned integers.
   * Reverts on division by zero.
   */
  static div(a: u256, b: u256): u256 {
    assert(u256.gt(b, u256.Zero), 'SafeMath: division by zero');
    const c = u256.div(a, b);
    return c;
  }

  /**
   * Returns the remainder of dividing two unsigned integers.
   * Reverts with custom message when dividing by zero.
   */
  static mod(a: u256, b: u256): u256 {
    assert(!b.isZero(), 'SafeMath: modulo by zero');
    return u256.rem(a, b);
  }
}
\`\`\`
*   *Find the full library in:* \`[https://github.com/NaDasai/eagle-finance/blob/55592c9b82fd08d47c5741aca76e97a5673b3061/smart-contracts/assembly/lib/safeMath.ts#L107]\`

## 2. Integer Square Root (sqrt) for u256

**Problem:** Calculating the integer square root is a common requirement in AMM formulas (e.g., for initial liquidity minting based on geometric mean in Uniswap V2 style pools), but it's not a standard operation provided for \`u256\` types, and implementations were not readily available as standard libraries in the Massa ecosystem.

**Contribution:** EagleFi includes an efficient implementation of the integer square root function for \`u256\`, utilizing the Babylonian method (or Newton's method).

**Benefits for the Ecosystem:** Provides a readily available mathematical utility essential for developers implementing various DeFi protocols, particularly AMMs, on Massa, filling a gap in standard \`u256\` functionality.

**Implementation Snippet:**

\`\`\`typescript
/**
 * Returns the square root of an unsigned integer.
 */
static sqrt(a: u256): u256 {
  assert(!a.isZero(), 'SafeMath: sqrt of zero');
  let x = a;
  let y = u256.add(u256.div(x, u256.from(2)), u256.One);
  while (u256.lt(y, x)) {
    x = y;
    y = u256.div(u256.add(u256.div(a, x), x), u256.from(2));
  }
  return x;
}
\`\`\`
*   *Find the implementation within:* \`[https://github.com/NaDasai/eagle-finance/blob/55592c9b82fd08d47c5741aca76e97a5673b3061/smart-contracts/assembly/lib/safeMath.ts#L184]\`

## 3. Reference Implementation: Practical SafeMath Usage

**Context:** While providing libraries like \`SafeMath256\` is useful, demonstrating their practical application within core DeFi logic involving \`u256\` offers valuable insights for developers navigating these large number types.

**Contribution:** The EagleFi codebase serves as a reference, showcasing how \`SafeMath256\` (including \`sqrt\`) is integrated into essential AMM calculations like fee determination and liquidity minting, all operating on \`u256\` values.

**Benefits for the Ecosystem:** Offers concrete examples for developers implementing similar financial logic requiring \`u256\`, reducing potential errors and promoting best practices for safe arithmetic in Massa smart contracts.

**Example Snippets:**

\`\`\`typescript
// Fee Calculation Example
/**
 * Calculates the fee from a given input amount and fee rate.
 */
export function getFeeFromAmount(inputAmount: u256, feeRate: u64): u256 {
  // convert fee rate to u256
  const feeRate256 = u256.fromU64(feeRate);

  // Calculate the fee as: (inputAmount * feeRate256) / SCALING_FACTOR
  const product = SafeMath256.mul(inputAmount, feeRate256); // Using SafeMath
  const fee = SafeMath256.div(product, SCALING_FACTOR); // Using SafeMath

  return fee;
}

// Amount Without Fee Example
/**
 * Calculates the amount without fee from a total amount and a fee rate.
 */
export function getAmountWithoutFee(totalAmount: u256, feeRate: u64): u256 {
  const feeRateU256 = u256.fromU64(feeRate);
  const denominator = SafeMath256.add(SCALING_FACTOR, feeRateU256); // Using SafeMath
  const amountWithoutFee = SafeMath256.div( // Using SafeMath
    SafeMath256.mul(totalAmount, SCALING_FACTOR), // Using SafeMath
    denominator,
  );

  return amountWithoutFee;
}

// Initial Liquidity Calculation Example
// (Inside a function like _addLiquidity)
if (reserveA == u256.Zero && reserveB == u256.Zero) {
  // Initial liquidity: liquidity = sqrt(amountA * amountB)
  const product = SafeMath256.mul(normAmountA, normAmountB); // Using SafeMath
  // liquidity = sqrt(product) - MINIMUM_LIQUIDITY
  liquidity = SafeMath256.sub(SafeMath256.sqrt(product), MINIMUM_LIQUIDITY); // Using SafeMath sqrt & sub
  isInitialLiquidity = true;
} else {
  // Proportional liquidity calculation using SafeMath div and mul...
  const amountBOptimal = SafeMath256.div( // Using SafeMath
    SafeMath256.mul(normAmountA, normReserveB), // Using SafeMath
    normReserveA,
  );
  // ... more calculations using SafeMath ...
  const liquidityA = SafeMath256.div( // Using SafeMath
    SafeMath256.mul(normAmountA, totalSupply), // Using SafeMath
    normReserveA,
  );
  const liquidityB = SafeMath256.div( // Using SafeMath
    SafeMath256.mul(normAmountBFinal, totalSupply), // Using SafeMath
    normReserveB,
  );
  liquidity = u256.min(liquidityA, liquidityB);
}
\`\`\`
*   *Find more examples in:* \`[assembly/libraries/basicPoolMath.ts, assembly/contracts/pools/BasicPool.ts]\`

## 4. Reference Implementation: u256 Support in Liquidity Management

**Context:** Many DeFi protocols need to handle potentially huge token balances and liquidity figures, especially with high-decimal tokens. Using \`u64\` can lead to overflows. Ensuring core components work natively with \`u256\` is crucial.

**Contribution:** EagleFi's core components, such as the logic managing LP token minting/burning and balance tracking (\`LiquidityManager\`), are designed to operate natively with \`u256\`. This ensures the protocol can handle large values accurately and avoids potential overflow issues present with smaller integer types.

**Benefits for the Ecosystem:** Provides a design pattern and reference for building Massa contracts that are robust and capable of handling the large numerical scales common in DeFi, ensuring compatibility and accuracy when using \`u256\`.

**Implementation Snippets:**

\`\`\`typescript
// Generic LiquidityManager structure (conceptual)
export class LiquidityManager<T> { // T can be u256
  private balancePrefix: u8;
  private allowancePrefix: u8;

  constructor(storagePrefixManager: StoragePrefixManager) {
    this.balancePrefix = storagePrefixManager.newPrefix();
    this.allowancePrefix = storagePrefixManager.newPrefix();
  }

  // Methods...
}

// Internal update logic handling u256 specifically
private _updateAmount(key: StaticArray<u8>, amount: T, increase: bool): void {
  var newAmount = this._getOrNull(key);
  if (increase) {
    if (idof<T>() == idof<u256>()) { // Check if T is u256
      newAmount = u256.add(newAmount, amount) as T; // Use u256.add
    } else {
      // @ts-ignore arithmetic operations on generic types
      newAmount += amount;
    }
  } else {
    if (idof<T>() == idof<u256>()) { // Check if T is u256
      newAmount = u256.sub(newAmount, amount) as T; // Use u256.sub
    } else {
      // @ts-ignore arithmetic operations on generic types
      newAmount -= amount;
    }
  }
  Storage.set(key, this.serialize(newAmount));
}

// Usage example in Pool (adding liquidity)
// Create new liquidity manager representing the pool LP token
const storagePrefixManager = new StoragePrefixManager();
// Instantiate specifically for u256 LP tokens
const liquidityManager = new LiquidityManager<u256>(storagePrefixManager);

// Mint LP tokens (u256 amount) to user
export function addLiquidity(binaryArgs: StaticArray<u8>): addLiquidityData {
  // ... calculation logic resulting in 'liquidity' as u256 ...

  // Permanently lock the first MINIMUM_LIQUIDITY tokens
  if (isInitialLiquidity) {
    // Mint MINIMUM_LIQUIDITY tokens to empty address
    liquidityManager.mint(new Address(''), MINIMUM_LIQUIDITY); // MINIMUM_LIQUIDITY is u256
  }

  // Mint LP tokens to user
  liquidityManager.mint(callerAddress, liquidity); // liquidity is u256

  // ... update reserves ...

  // Return data including u256 amounts
  return new addLiquidityData(
    contractAddress.toString(),
    callerAddress.toString(),
    finalAmountA, // u256
    finalAmountB, // u256
    liquidity,    // u256
    newResA,      // u256
    newResB,      // u256
  );
}
\`\`\`
*   *Find the implementation details in:* \`[assembly/contracts/libraries/LiquidityManager.ts, assembly/contracts/pools/BasicPool.ts]\`

## 5. Conclusion

EagleFi's development journey necessitated significant work in establishing robust and safe handling of \`u256\` values, an area with limited examples in the early Massa ecosystem. By contributing the \`SafeMath256\` library, an efficient \`sqrt\` implementation for \`u256\`, and providing reference implementations for their practical usage within core DeFi logic like liquidity management, we hope to lower the barrier for other developers building sophisticated financial applications on Massa. We encourage the community to utilize, adapt, and build upon these \`u256\` focused contributions.
`;

export const blogContent3 = `
<section class="prose max-w-none">
  <p class="lead">In the revolutionary world of decentralized finance (DeFi), liquidity pools have emerged as the fundamental building blocks enabling seamless asset trading without traditional intermediaries. These innovative smart contract-based reservoirs of cryptocurrency are powering a new generation of financial infrastructure.</p>

  <h2>What Are Liquidity Pools?</h2>
  <p>Liquidity pools are crowdsourced collections of digital assets locked in smart contracts that facilitate decentralized trading. Unlike traditional order books, these pools:</p>
  <ul>
    <li>Contain paired assets (e.g., ETH/USDC)</li>
    <li>Enable instant trades through automated pricing</li>
    <li>Reward providers with trading fees</li>
  </ul>

  <div class="bg-yellow-100 p-4 rounded-lg border-2 border-[#1E1E1E] my-6">
    <strong>Real-World Analogy:</strong> Imagine a community-owned bank vault where neighbors pool their savings to enable local lending and borrowing - that's the essence of a liquidity pool in DeFi.
  </div>

  <h2>The Engine Behind the Magic: Automated Market Makers (AMMs)</h2>
  <p>AMMs use mathematical formulas to determine asset prices algorithmically. The most common model is the <strong>Constant Product Formula</strong>:</p>

  <pre class="bg-gray-800 text-white p-4 rounded-lg my-4"><code>x * y = k
// Where:
// x = Quantity of Token A
// y = Quantity of Token B
// k = Constant product</code></pre>

  <p>This simple equation ensures:</p>
  <ul>
    <li>Continuous liquidity regardless of trade size</li>
    <li>Automatic price adjustments based on pool ratios</li>
    <li>Protection against complete liquidity depletion</li>
  </ul>

  <h3>Trade Example</h3>
  <p>Consider a ETH/USDC pool with:</p>
  <ul>
    <li>100 ETH ($200,000)</li>
    <li>200,000 USDC</li>
  </ul>
  <p>A user swaps 1 ETH for USDC:</p>
  <ol>
    <li>New ETH balance: 101</li>
    <li>New USDC balance: 200,000 / (100/99) ≈ 198,019.80</li>
    <li>User receives ≈ 1,980.20 USDC</li>
  </ol>

  <h2>Why Liquidity Pools Matter</h2>
  <div class="grid md:grid-cols-2 gap-6 my-8">
    <div class="bg-green-100 p-4 rounded-lg border-2 border-[#1E1E1E]">
      <h3 class="!mt-0">Benefits</h3>
      <ul>
        <li>24/7 market availability</li>
        <li>Lower barriers to market making</li>
        <li>Earn 0.3% fees on Uniswap-style DEXs</li>
      </ul>
    </div>
    
    <div class="bg-red-100 p-4 rounded-lg border-2 border-[#1E1E1E]">
      <h3 class="!mt-0">Risks</h3>
      <ul>
        <li>Impermanent loss</li>
        <li>Smart contract vulnerabilities</li>
      </ul>
    </div>
  </div>

  <h2>Advanced Pool Types</h2>
  <table class="w-full my-6 border-collapse border-2 border-[#1E1E1E]">
    <thead class="bg-gray-100">
      <tr>
        <th class="p-2 text-left">Type</th>
        <th class="p-2 text-left">Example</th>
        <th class="p-2 text-left">Use Case</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td class="p-2 border-t-2 border-[#1E1E1E]">Weighted</td>
        <td class="p-2 border-t-2 border-[#1E1E1E]">Balancer</td>
        <td class="p-2 border-t-2 border-[#1E1E1E]">Multi-asset pools</td>
      </tr>
      <tr>
        <td class="p-2 border-t-2 border-[#1E1E1E]">StableSwap</td>
        <td class="p-2 border-t-2 border-[#1E1E1E]">Curve Finance</td>
        <td class="p-2 border-t-2 border-[#1E1E1E]">Stablecoin trading</td>
      </tr>
    </tbody>
  </table>

  <h2>Becoming a Liquidity Provider</h2>
  <p>To participate:</p>
  <ol>
    <li>Choose a trusted DEX (e.g., EagleFi)</li>
    <li>Deposit equal value of both assets</li>
    <li>Get LP tokens representing your share</li>
  </ol>

  <div class="bg-blue-100 p-4 rounded-lg border-2 border-[#1E1E1E] my-6">
    <strong>Pro Tip:</strong> Use impermanent loss calculators and consider stablecoin pairs to minimize risk when starting out.
  </div>

  <h2>The Future of Liquidity Pools</h2>
  <p>Emerging innovations include:</p>
  <ul>
    <li>Concentrated liquidity (Uniswap V3)</li>
    <li>Cross-chain pools</li>
    <li>Insurance-protected pools</li>
  </ul>

  <p class="text-lg font-semibold mt-8">Liquidity pools have democratized market making, creating new opportunities for investors while powering the DeFi revolution. As these mechanisms evolve, they promise to make decentralized finance more efficient and accessible than ever before.</p>
</section>
`;
export const FEES_SCALING_FACTOR = 10_000;
