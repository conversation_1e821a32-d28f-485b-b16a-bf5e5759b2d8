import { lazy, Suspense, useEffect } from "react";
import CreatePool from "./pages/CreatePool";
const CreateTokenForm = lazy(() => import("./pages/CreateToken"));
import Header from "./components/Header";
import { Outlet, Route, Routes, useLocation } from "react-router-dom";
import Swap from "./pages/Swap";
import Portfolio from "./pages/Portfolio";
import Pools from "./pages/Pools";
import Footer from "./components/Footer";
// const Docs = lazy(() => import("./pages/Docs"));
// const DevDocs = lazy(() => import("./pages/DevDocs"));
const BuyCrypto = lazy(() => import("./pages/BuyCrypto"));
// const Bridge = lazy(() => import("./pages/Bridge"));
const Lottery = lazy(() => import("./pages/Lottery"));
const LeaderBoard = lazy(() => import("./pages/LeaderBoard"));
// const Blog = lazy(() => import("./pages/Blog"));
const Send = lazy(() => import("./pages/Send"));
const EagleAssistantAI = lazy(() => import("./pages/EagleAssistantAI"));
const TermsOfService = lazy(() => import("./pages/TermsOfService"));
import useAccountSync from "./hooks/useAccountSync";
import TokenStats from "./pages/TokenStats";
const SwapWidgetCreator = lazy(() => import("./pages/SwapWidgetCreator"));
import SwapWidgetContainer from "./components/SwapWidgetContainer";
import ScrollToTop from "./components/ScrollToTop";
import { Spinner } from "@massalabs/react-ui-kit";
import ReactGA from "react-ga4";
import { SEO } from "./SEO";
import MaintenancePage from "./pages/MaintenancePage";

const FlappyBird = lazy(() => import("./pages/FlappyBird"));
// import { Blog, BlogPost } from "./pages/Blog";
const Blog = lazy(() =>
  import("./pages/Blog").then((module) => ({ default: module.Blog }))
);
const BlogPost = lazy(() =>
  import("./pages/Blog").then((module) => ({ default: module.BlogPost }))
);

const Layout = () => (
  <div className="flex flex-col justify-between min-h-screen">
    <Header />
    <Outlet />
    <Footer />
  </div>
);

function App() {
  useAccountSync();
  const location = useLocation();
  useEffect(() => {
    ReactGA.send({ hitType: "pageview", page: location.pathname });
  }, [location]);

  const getQueryParams = (search: string) => {
    const params = new URLSearchParams(search);
    return {
      fromTokenAddress: params.get("fromToken") || undefined,
      toTokenAddress: params.get("toToken") || undefined,
      fixFromToken: params.get("fixFromToken") === "true",
      fixToToken: params.get("fixToToken") === "true",
      theme: (params.get("theme") as "light" | "dark") || "light",
    };
  };

  return (
    <>
      <SEO />
      <ScrollToTop />
      <Routes>
        {/* Routes with Header and Footer */}
        <Route
          element={
            <Suspense fallback={<Spinner />}>
              <Layout />
            </Suspense>
          }
        >
          <Route path="/" element={<Swap />} />
          <Route
            path="/maintenance"
            element={
              <Suspense fallback={<div>Loading...</div>}>
                <MaintenancePage />
              </Suspense>
            }
          />
          <Route path="/create-token" element={<CreateTokenForm />} />
          <Route path="/create-pool" element={<CreatePool />} />
          <Route path="/portfolio" element={<Portfolio />} />
          <Route path="/liquidity" element={<Pools />} />
          {/* <Route path="/docs" element={<Docs />} />
          <Route path="/devdocs" element={<DevDocs />} /> */}
          <Route path="/bridge" element={<BuyCrypto />} />
          {/* <Route path="/bridge" element={<Bridge />} /> */}
          <Route path="/lottery" element={<Lottery />} />
          <Route path="/leaderboard" element={<LeaderBoard />} />

          <Route path="/send" element={<Send />} />
          <Route path="/eagle-assistant" element={<EagleAssistantAI />} />
          <Route path="/token/:tokenAddress" element={<TokenStats />} />
          <Route path="/swap-widget-creator" element={<SwapWidgetCreator />} />
          <Route path="/terms-of-service" element={<TermsOfService />} />
          <Route path="/flappy-eagle" element={<FlappyBird />} />
          <Route path="/blog">
            <Route index element={<Blog />} />
            <Route path=":slug" element={<BlogPost />} />
          </Route>
        </Route>

        {/* Standalone route without Header and Footer */}
        <Route
          path="/swap-widget"
          element={
            <SwapWidgetContainer {...getQueryParams(window.location.search)} />
          }
        />
      </Routes>
    </>
  );
}

export default App;
