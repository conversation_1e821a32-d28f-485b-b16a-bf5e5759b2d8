// src/redux/features/liquidityTokensSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Token } from "./tokensSlice";
import { Pool } from "../../pages/Pools";
import axiosInstance from "../../lib/axios/axiosInstance";

export interface BackendToken {
  address: string;
  symbol: string;
  logo: string;
  name: string;
  price_mas: number;
  price_usd: number;
  decimals: number;
  is_native: boolean;
  total_supply: number;
  circulated_supply: number;
  description?: string;
  status?: string;
  created_by?: string;
  created_at?: string;
  is_pausable?: boolean;
  is_mintable?: boolean;
  is_burnable?: boolean;
  is_paused?: boolean;
  pool_count?: number;
}

const initialState = {
  tokens: [] as Token[],
  pools: [] as Pool[],
  lastUpdated: Date.now(),
  loading: false,
  error: null as string | null,
};

export const fetchLiquidityTokens = createAsyncThunk(
  "liquidityTokens/fetchLiquidityTokens",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get("/tokens/liquidity");
      return response.data as BackendToken[];
    } catch (error) {
      return rejectWithValue("Failed to fetch liquidity tokens");
    }
  }
);

const liquidityTokensSlice = createSlice({
  name: "liquidityTokens",
  initialState,
  reducers: {
    setPools: (state, action: PayloadAction<Pool[]>) => {
      state.pools = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchLiquidityTokens.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLiquidityTokens.fulfilled, (state, action) => {
        state.loading = false;
        state.lastUpdated = Date.now();
        state.tokens = action.payload.map((backendToken) => ({
          symbol: backendToken.symbol,
          name: backendToken.name,
          logo: backendToken.logo,
          price: {
            mas: backendToken.price_mas,
            usd: backendToken.price_usd,
          },
          balance: 0,
          address: backendToken.address,
          decimals: backendToken.decimals,
          is_native: backendToken.is_native,
          totalSupply: backendToken.total_supply,
          circulatingSupply: backendToken.circulated_supply,
        }));
      })
      .addCase(fetchLiquidityTokens.rejected, (state, action) => {
        state.loading = false;
        state.error =
          (action.payload as string) || "Failed to fetch liquidity tokens";
      });
  },
});

export const { setPools } = liquidityTokensSlice.actions;

export default liquidityTokensSlice.reducer;
