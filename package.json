{"name": "hello-world-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@massalabs/massa-web3": "^5.2.1-dev.20250627123813", "@massalabs/react-ui-kit": "^1.1.1-dev.20250619123600", "@massalabs/wallet-provider": "^3.2.1-dev.20250627130552", "@reduxjs/toolkit": "^2.5.0", "@rollup/plugin-commonjs": "^28.0.3", "axios": "^1.7.9", "buffer": "^6.0.3", "date-fns": "^4.1.0", "eventsource-parser": "^2.0.1", "immer": "^10.1.1", "lightweight-charts": "^5.0.2", "path": "^0.12.7", "prismjs": "^1.30.0", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-fast-marquee": "^1.6.5", "react-ga4": "^2.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.0.2", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^11.0.2", "redux-persist": "^6.0.0", "remark": "^14.0.3", "remark-gfm": "^4.0.1", "remark-slug": "^6.0.0", "unified": "^11.0.5", "use-immer": "^0.10.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/dot-object": "^2.1.6", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10", "vite-plugin-prerender": "^1.0.8"}}