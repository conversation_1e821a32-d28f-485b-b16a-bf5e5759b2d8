import { useSelector } from "react-redux";
import { RootState, useAppDispatch } from "../redux/store";
import {
  Args,
  Call,
  formatUnits,
  Mas,
  MAX_GAS_EXECUTE,
  MRC20,
  Multicall,
  Operation,
  parseMas,
  parseUnits,
  PublicAPI,
  PublicApiUrl,
  SmartContract,
} from "@massalabs/massa-web3";
import { toast } from "react-toastify";
import { Token } from "../redux/features/tokensSlice";
// import { IBestPool } from "../lib/types";
import {
  formatStringDecimals,
  MASSA_ROLLS_ADDRESS,
  shortenAddress,
  validateInput,
  waitForExecution,
} from "../lib/utils";
import { useAccountStore } from "@massalabs/react-ui-kit";
import { useState, useRef, useEffect, useCallback, useMemo } from "react";
import ConnectWalletModal from "../components/ConnectWalletModal";
import SettingsModal from "./SettingsModal";
import {
  BackendToken,
  fetchLiquidityTokens,
} from "../redux/features/liquidityTokensSlice";
import axiosInstance from "../lib/axios/axiosInstance";
import { buyRolls, sellRolls } from "../services/rolls";
import { AddressInfo } from "@massalabs/massa-web3/dist/esm/generated/client-types";
import { SwapPath } from "../lib/structs/swapPath";
import TokenLogo from "./TokenLogo";
import AddressWithCopy from "./AddressWithCopy";
import {
  formatScientificToFixed,
  logError,
  prettyPrice,
  withRetry,
} from "../lib/utils2";

interface IToken {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logo: string;
  is_native: boolean;
  // ..
}

interface IPool {
  a_token: IToken;
  b_token: IToken;
  pool_address: string;
  a_reserve: number;
  b_reserve: number;
}

interface IBestPool {
  amount_in: number;
  amount_out: number;
  min_amount_out: number;
  token_in: IToken;
  token_out: IToken;
  pool: IPool;
  is_transfer_from: boolean;
  receiver_address: string;
}

interface ISwapEstimationResponse {
  amount_in?: number;
  amount_out?: number;
  min_amount_out: number;
  amount_in_price: number;
  amount_out_price: number;
  price_impact: number;
  route: IBestPool[];
  deadline: number;
}

interface ISwapWidgetProps {
  defaultToTokenAddress?: string;
  fromTokenAddress?: string;
  toTokenAddress?: string;
  fixFromToken?: boolean;
  fixToToken?: boolean;
  theme?: "light" | "dark";
}

const mapBackendTokenToToken = (backendToken: BackendToken): Token => ({
  symbol: backendToken.symbol,
  name: backendToken.name,
  logo: backendToken.logo,
  price: {
    mas: backendToken.price_mas,
    usd: backendToken.price_usd,
  },
  balance: 0,
  address: backendToken.address,
  decimals: backendToken.decimals,
  is_native: backendToken.is_native,
  totalSupply: backendToken.total_supply,
  circulatingSupply: backendToken.circulated_supply,
});

const SwapWidget = ({
  defaultToTokenAddress,
  fromTokenAddress,
  toTokenAddress,
  fixFromToken = false,
  fixToToken = false,
  theme = "light",
}: ISwapWidgetProps) => {
  const [toggleConnectWalletModal, setToggleConnectWalletModal] =
    useState(false);
  const { connectedAccount, currentWallet } = useAccountStore();
  const [network, setNetwork] = useState<string | null>(null);
  const liquidityTokens = useSelector(
    (state: RootState) => state.liquidityTokens.tokens,
    (prev, next) => JSON.stringify(prev) === JSON.stringify(next)
  );
  const wmasToken = liquidityTokens.find((token) => token.symbol === "WMAS");
  const [fromToken, setFromToken] = useState(liquidityTokens[0]);
  const [toToken, setToToken] = useState(liquidityTokens[1]);
  const [showFromDropdown, setShowFromDropdown] = useState(false);
  const [showToDropdown, setShowToDropdown] = useState(false);

  const [fromAmount, setFromAmount] = useState<string>("");
  const [toAmount, setToAmount] = useState<string>("");
  const [minAmountOut, setMinAmountOut] = useState<number>(0);

  const [fromTokenBalance, setFromTokenBalance] = useState("0");
  const [toTokenBalance, setToTokenBalance] = useState("0");

  const [isRotating, setIsRotating] = useState(false);

  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [slippage, setSlippage] = useState(() => {
    const savedSlippage = localStorage.getItem("slippage");
    return savedSlippage ? parseFloat(savedSlippage) : 5;
  });

  const fromRef = useRef<HTMLDivElement>(null);
  const toRef = useRef<HTMLDivElement>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [bestPool, setBestPool] = useState<IBestPool | null>(null);
  // const [poolContract, setPoolContract] = useState<SmartContract | null>(null);
  const [swapRoute, setSwapRoute] = useState<IBestPool[] | null>(null);

  const [fromUsd, setFromUsd] = useState<string>("0.00");
  const [toUsd, setToUsd] = useState<string>("0.00");
  // const timerRef = useRef<NodeJS.Timeout | null>(null);

  const fromTimerRef = useRef<NodeJS.Timeout | null>(null);
  const toTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isFromAmountUpdatingDueToToChange = useRef(false);
  const fromInputRef = useRef<HTMLInputElement>(null);

  const [priceImpact, setPriceImpact] = useState<number>(0);
  const [deadline, setDeadline] = useState<number>(0);

  const [fromSearchQuery, setFromSearchQuery] = useState("");
  const [fromFilteredTokens, setFromFilteredTokens] = useState<Token[]>([]);
  const [toSearchQuery, setToSearchQuery] = useState("");
  const [toFilteredTokens, setToFilteredTokens] = useState<Token[]>([]);

  useEffect(() => {
    const fetchNetworkInfo = async () => {
      if (connectedAccount && currentWallet) {
        const networkInfo = await currentWallet.networkInfos();
        const networkName = networkInfo?.name ?? null;
        setNetwork(networkName);
      } else {
        setNetwork(null);
      }
    };

    fetchNetworkInfo();

    const handleNetworkChange = async () => {
      await fetchNetworkInfo();
    };

    if (currentWallet) {
      currentWallet.listenNetworkChanges(handleNetworkChange);
    }
  }, [connectedAccount, currentWallet]);

  useEffect(() => {
    if (showFromDropdown) {
      const handler = setTimeout(async () => {
        try {
          const response = await axiosInstance.get(
            `/tokens/liquidity?query=${encodeURIComponent(fromSearchQuery)}`
          );
          setFromFilteredTokens(response.data.map(mapBackendTokenToToken));
        } catch (error) {
          console.error("Error searching tokens:", error);
        }
      }, 300);

      return () => clearTimeout(handler);
    }
  }, [fromSearchQuery, showFromDropdown]);

  useEffect(() => {
    if (showToDropdown) {
      const handler = setTimeout(async () => {
        try {
          const response = await axiosInstance.get(
            `/tokens/liquidity?query=${encodeURIComponent(toSearchQuery)}`
          );
          setToFilteredTokens(response.data.map(mapBackendTokenToToken));
        } catch (error) {
          console.error("Error searching tokens:", error);
        }
      }, 300);

      return () => clearTimeout(handler);
    }
  }, [toSearchQuery, showToDropdown]);

  // Reset search when closing dropdowns
  useEffect(() => {
    if (!showFromDropdown) {
      setFromSearchQuery("");
      setFromFilteredTokens(liquidityTokens);
    }
  }, [showFromDropdown, liquidityTokens]);

  useEffect(() => {
    if (!showToDropdown) {
      setToSearchQuery("");
      setToFilteredTokens(liquidityTokens);
    }
  }, [showToDropdown, liquidityTokens]);

  useEffect(() => {
    setFromFilteredTokens(liquidityTokens);
    setToFilteredTokens(liquidityTokens);
  }, [liquidityTokens]);
  const themeStyles = useMemo(
    () =>
      theme === "dark"
        ? {
            container: "bg-gray-900 text-white border-gray-700",
            inputBg: "bg-gray-800 text-white",
            buttonBg: "bg-[#1E1E1E] text-[#FFFFFF]",
            dropdown: "bg-gray-800 border-gray-700 ",
            dropdownItem: "hover:bg-gray-700",
            textPrimary: "text-white",
            textSecondary: "text-black",
            textInput: "text-white",
            border: "border-gray-700",
          }
        : {
            container: "bg-white text-black border-[#e3e6f1]",
            inputBg: "bg-[#E6EBEC] text-[#6e7aaa]",
            buttonBg: "bg-[#1E1E1E] text-[#FFFFFF]",
            dropdown: "bg-white border-[#1E1E1E] ",
            dropdownItem: "hover:bg-[#f1f5f9]",
            textPrimary: "text-black",
            textSecondary: "text-black",
            textInput: "text-[#6e7aaa]",
            border: "border-[#1E1E1E]",
          },
    [theme]
  );

  useEffect(() => {
    if (fromInputRef.current) {
      fromInputRef.current.focus();
    }
  }, []);

  useEffect(() => {
    localStorage.setItem("slippage", slippage.toString());
  }, [slippage]);
  const dispatch = useAppDispatch();
  useEffect(() => {
    dispatch(fetchLiquidityTokens());
  }, [dispatch, fetchLiquidityTokens]);

  useEffect(() => {
    if (liquidityTokens.length > 0) {
      // Set "From" token if provided and not fixed
      if (fromTokenAddress) {
        const token = liquidityTokens.find(
          (t) => t.address === fromTokenAddress
        );
        if (token) setFromToken(token);
      } else {
        setFromToken(liquidityTokens[0]);
      }

      // Set "To" token if provided or use default
      let initialToToken = liquidityTokens[1];
      if (toTokenAddress) {
        const token = liquidityTokens.find((t) => t.address === toTokenAddress);
        if (token) initialToToken = token;
      } else if (defaultToTokenAddress) {
        const token = liquidityTokens.find(
          (t) => t.address === defaultToTokenAddress
        );
        if (token) initialToToken = token;
      }
      setToToken(initialToToken);
    }
  }, [
    liquidityTokens,
    fromTokenAddress,
    toTokenAddress,
    defaultToTokenAddress,
  ]);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (fromRef.current && !fromRef.current.contains(e.target as Node)) {
        setShowFromDropdown(false);
      }
      if (toRef.current && !toRef.current.contains(e.target as Node)) {
        setShowToDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // useEffect(() => {
  //   const fetchBestPool = async () => {
  //     if (!fromToken || !toToken) return;

  //     if (fromToken.address === toToken.address) {
  //       setBestPool(null);
  //       setSwapRoute(null);
  //       return;
  //     }

  //     const isNativeSwap =
  //       (fromToken.symbol === "MAS" && toToken.symbol === "WMAS") ||
  //       (fromToken.symbol === "WMAS" && toToken.symbol === "MAS");
  //     const isRollSwap =
  //       (fromToken.symbol === "MAS" && toToken.symbol === "ROLL") ||
  //       (fromToken.symbol === "ROLL" && toToken.symbol === "MAS");

  //     if (isNativeSwap || isRollSwap) {
  //       setBestPool(null);
  //       setSwapRoute(null);
  //       return;
  //     }

  //     try {
  //       const response = await axiosInstance.post<ISwapEstimationResponse>(
  //         "/estimate/swap",
  //         {
  //           amount_in: 0.000001, // Minimal amount to trigger best_pool and need to check with ayoub
  //           token_in_address: fromToken.address,
  //           token_out_address: toToken.address,
  //           slippage: slippage,
  //         }
  //       );

  //       if (response.data.route && response.data.route.length > 0 && connectedAccount) {
  //         const first_pool = response.data.route[0];
  //         setBestPool(first_pool);
  //         const poolContract = new SmartContract(
  //           connectedAccount,
  //           first_pool.pool.pool_address
  //         );
  //         setPoolContract(poolContract);
  //         setSwapRoute(response.data.route);
  //       }
  //     } catch (error) {
  //       console.error("Error fetching best pool:", error);
  //       setBestPool(null);
  //       setSwapRoute(null);
  //     }
  //   };

  //   fetchBestPool();
  // }, [connectedAccount, fromToken, toToken, slippage]);

  const handleSwap = () => {
    if (fromToken.address === toToken.address) {
      toast.error("Cannot swap the same token");
      return;
    }
    setIsRotating(true);
    setTimeout(() => {
      setIsRotating(false);
    }, 300);

    const currentFrom = fromAmount;
    const currentTo = toAmount;

    const temp = fromToken;
    setFromToken(toToken);
    setToToken(temp);

    setFromAmount(currentTo);
    setToAmount(currentFrom);
  };

  useEffect(() => {
    if (isFromAmountUpdatingDueToToChange.current) {
      isFromAmountUpdatingDueToToChange.current = false;
      return;
    }
    if (fromAmount) {
      handleFromAmountChange(fromAmount);
    }
  }, [fromToken, toToken, fromAmount]); // Re-run when btn swap clokcked

  const handleFromAmountChange = async (value: string) => {
    if (!value || value.trim() === "") {
      setFromAmount("");
      setToAmount("");
      setFromUsd("0.00");
      setToUsd("0.00");
      setPriceImpact(0);
      return;
    }
    if (!validateInput(value)) {
      setFromAmount("");
      setToAmount("");
      setFromUsd("0.00");
      setToUsd("0.00");
      setPriceImpact(0);
      return;
    }

    if (fromToken.address === toToken.address) {
      toast.error("Select different tokens to swap");
      setFromAmount("");
      setToAmount("");
      setFromUsd("0.00");
      setToUsd("0.00");
      setPriceImpact(0);
      return;
    }

    if (!value || !wmasToken) {
      setToAmount("");
      setFromUsd("0.00");
      setToUsd("0.00");
      setPriceImpact(0);
      return;
    }

    const isNativeSwap =
      (fromToken.symbol === "MAS" && toToken.symbol === "WMAS") ||
      (fromToken.symbol === "WMAS" && toToken.symbol === "MAS");

    setFromAmount(value);
    // Handle native swap 1:1 ratio
    if (isNativeSwap) {
      setToAmount(value);
      setMinAmountOut(Number(value));
      setFromUsd((Number(value) * fromToken.price.usd).toFixed(2));
      setToUsd((Number(value) * toToken.price.usd).toFixed(2));
      setPriceImpact(0);
      return;
    }
    const isRollSwap =
      (fromToken.symbol === "MAS" && toToken.symbol === "ROLL") ||
      (fromToken.symbol === "ROLL" && toToken.symbol === "MAS");

    if (isRollSwap) {
      const fromVal = Number(value);
      if (fromToken.symbol === "MAS") {
        const rolls = fromVal / 100;
        setToAmount(rolls.toString());
        setMinAmountOut(rolls * (1 - slippage / 100));
      } else {
        const mas = fromVal * 100;
        setToAmount(mas.toString());
        setMinAmountOut(mas * (1 - slippage / 100));
      }
      setFromUsd((fromVal * fromToken.price.usd).toFixed(2));
      setToUsd((Number(toAmount) * toToken.price.usd).toFixed(2));
      setPriceImpact(0);
      return;
    }

    // Debounce API call
    if (fromTimerRef.current) {
      clearTimeout(fromTimerRef.current);
    }

    fromTimerRef.current = setTimeout(async () => {
      const fromVal = Number(value);
      if (fromVal) {
        try {
          const response = await axiosInstance.post<ISwapEstimationResponse>(
            "/estimate/swap",
            {
              amount_in: fromVal,
              slippage: slippage,
              token_in_address: fromToken.address,
              token_out_address: toToken.address,
            }
          );

          const minThreshold = 1 / Math.pow(10, toToken.decimals);

          if ((response?.data?.amount_out ?? 0) < minThreshold) {
            setToAmount("0.00000000");
            setMinAmountOut(0);
            toast.error(
              `Amount too small to swap. Minimum: ${minThreshold} ${toToken.symbol}`
            );
            return;
          }

          setToAmount(
            response.data.amount_out
              ? formatScientificToFixed(
                  response.data.amount_out,
                  toToken.decimals
                )
              : ""
          );
          setBestPool(response.data.route[0]);
          setSwapRoute(response.data.route);
          setMinAmountOut(response.data.min_amount_out);
          setFromUsd(response.data.amount_in_price.toFixed(2));
          setToUsd(response.data.amount_out_price.toFixed(2));
          setPriceImpact(response.data.price_impact || 0);
          setDeadline(response.data.deadline);
        } catch (error) {
          console.error("Error estimating swap:", error);
          setToAmount("");
          setFromUsd("0.00");
          setToUsd("0.00");
          setPriceImpact(0);
        }
      }
    }, 500);
  };

  const handleToAmountChange = (value: string) => {
    if (!validateInput(value)) return;
    if (fromToken.address === toToken.address) {
      toast.error("Select different tokens to swap");
      setFromAmount("");
      setFromUsd("0.00");
      setToUsd("0.00");
      return;
    }

    // Handle native MAS/WMAS and ROLL swaps
    // const isRollSwap =
    //   (fromToken.symbol === "MAS" && toToken.symbol === "ROLL") ||
    //   (fromToken.symbol === "ROLL" && toToken.symbol === "MAS");
    // if (isRollSwap) {
    //   const toVal = Number(value);
    //   if (toToken.symbol === "ROLL") {
    //     const mas = toVal * 100;
    //     setFromAmount(mas.toString());
    //   } else {
    //     const rolls = toVal / 100;
    //     setFromAmount(rolls.toString());
    //   }
    //   setFromUsd((Number(fromAmount) * fromToken.price.usd).toFixed(2));
    //   setToUsd((toVal * toToken.price.usd).toFixed(2));
    //   return;
    // }
    const isNativeSwap =
      (fromToken.symbol === "MAS" && toToken.symbol === "WMAS") ||
      (fromToken.symbol === "WMAS" && toToken.symbol === "MAS");

    setToAmount(value);

    if (isNativeSwap) {
      const toVal = Number(value);
      // setFromAmount(toVal.toString());
      setFromAmount(value);
      setMinAmountOut(toVal);
      setFromUsd((toVal * fromToken.price.usd).toFixed(2));
      setToUsd((toVal * toToken.price.usd).toFixed(2));
      setPriceImpact(0);
      return;
    }

    // Debounce API call
    if (toTimerRef.current) {
      clearTimeout(toTimerRef.current);
    }

    toTimerRef.current = setTimeout(async () => {
      const toVal = Number(value);
      if (toVal) {
        try {
          const response = await axiosInstance.post<ISwapEstimationResponse>(
            "/estimate/swap/reverse",
            {
              amount_out: toVal,
              token_in_address: fromToken.address,
              token_out_address: toToken.address,
              slippage: slippage,
            }
          );

          // Update from amount with the required input
          isFromAmountUpdatingDueToToChange.current = true;
          const minThreshold = 1 / Math.pow(10, fromToken.decimals); // Threshold for "From" token

          if ((response.data.amount_in ?? 0) < minThreshold) {
            setFromAmount("0.00000000");
            setMinAmountOut(0);
            toast.error(
              `Amount too small to swap. Minimum: ${minThreshold} ${fromToken.symbol}`
            );
            return;
          }

          setFromAmount(
            response.data.amount_in
              ? formatScientificToFixed(
                  response.data.amount_in,
                  fromToken.decimals
                )
              : ""
          );
          setBestPool(response.data.route[0]);
          setSwapRoute(response.data.route);
          setMinAmountOut(response.data.min_amount_out);
          setFromUsd(response.data.amount_in_price.toFixed(2));
          setToUsd(response.data.amount_out_price.toFixed(2));
          setPriceImpact(response.data.price_impact || 0);
          setDeadline(response.data.deadline);
        } catch (error) {
          console.error("Error estimating swap:", error);
          if (
            typeof error === "object" &&
            error !== null &&
            "response" in error &&
            (error as any).response &&
            (error as any).response.status === 400 &&
            (error as any).response.data?.error === "No Enough liquidity"
          ) {
            toast.error("Insufficient liquidity for this swap");
          }
          setFromAmount("");
          setFromUsd("0.00");
          setToUsd("0.00");
        }
      }
    }, 500);
  };

  const handleWrapMAS = async () => {
    if (!connectedAccount) {
      toast.error("Please connect your wallet.");

      return;
    }
    setIsLoading(true);
    const loadingToastId = toast.loading("Wrapping MAS to WMAS...");
    try {
      console.log("wrapping MAS to wmas...");

      const contract = new SmartContract(
        connectedAccount as any,
        liquidityTokens.find((token) => token.symbol === "WMAS")!?.address
      );

      console.log("fromAmount", fromAmount);

      // Truncate only 9 decimals of the amount cause MAS supports only 9 decimals
      const amount = formatStringDecimals(fromAmount, fromToken.decimals);

      console.log("amount to wrap", amount);

      // console.log("fromAmount parseMas", parseMas(fromAmount));
      // console.log("fromAmount Mas str",  Mas.fromString(fromAmount));

      const args = new Args().addU64(Mas.fromString(amount));

      const operation = await contract.call("deposit", args.serialize(), {
        coins: Mas.fromString(amount),
      });

      console.log(`Operation ID: ${operation.id}`);

      const [, isSuccess] = await waitForExecution(operation);

      if (isSuccess) {
        toast.update(loadingToastId, {
          render: `Successfully wrapped ${amount} MAS.`,
          type: "success",
          isLoading: false,
          autoClose: 5000,
        });
        pollBalances(5);
        setFromAmount("");
        setToAmount("");
        setPriceImpact(0);
        setFromUsd("0.00");
        setToUsd("0.00");
        setMinAmountOut(0);
      } else {
        toast.update(loadingToastId, {
          render: "Failed to wrap MAS.",
          type: "error",
          isLoading: false,
          autoClose: 5000,
        });
      }
    } catch (error) {
      console.error("Error wrapping MAS to WMAS:", error);
      toast.update(loadingToastId, {
        render: "An error occurred while wrapping MAS.",
        type: "error",
        isLoading: false,
        autoClose: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnwrapMAS = async () => {
    if (!connectedAccount) {
      toast.error("Please connect your wallet.");
      return;
    }
    setIsLoading(true);
    const loadingToastId = toast.loading("Unwrapping WMAS to MAS...");

    try {
      console.log("unwrapping WMAS to MAS...");

      const contract = new SmartContract(
        connectedAccount as any,
        liquidityTokens.find((token) => token.symbol === "WMAS")!?.address
      );

      console.log("fromAmount", fromAmount);

      const amount = formatStringDecimals(fromAmount, fromToken.decimals);

      console.log("amount to unwrap", amount);

      const args = new Args()
        .addU64(Mas.fromString(amount))
        .addString(connectedAccount.address);

      const operation = await contract.call("withdraw", args.serialize());

      console.log(`Operation ID: ${operation.id}`);

      const [, isSuccess] = await waitForExecution(operation);

      if (isSuccess) {
        toast.update(loadingToastId, {
          render: `Successfully unwrapped ${amount} WMAS.`,
          type: "success",
          isLoading: false,
          autoClose: 5000,
        });
        pollBalances(3);
        setFromAmount("");
        setToAmount("");
        setPriceImpact(0);
        setFromUsd("0.00");
        setToUsd("0.00");
        setMinAmountOut(0);
      } else {
        toast.update(loadingToastId, {
          render: "Failed to unwrap WMAS.",
          type: "error",
          isLoading: false,
          autoClose: 5000,
        });
      }
    } catch (error) {
      console.error("Error unwrapping WMAS to MAS:", error);
      toast.update(loadingToastId, {
        render: "An error occurred while unwrapping WMAS.",
        type: "error",
        isLoading: false,
        autoClose: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSwapTokens = async () => {
    if (!connectedAccount) {
      toast.error("Please connect your wallet.");
      return;
    }

    if (!fromAmount || !toAmount) {
      toast.error("Please enter an amount.");
      return;
    }

    if (!wmasToken) {
      toast.error("WMAS token not found.");
      return;
    }
    if (!bestPool) {
      toast.error("No Pool Found.");
      return;
    }

    if (!swapRoute) {
      toast.error("No Swap Route Found.");
      return;
    }

    setIsLoading(true);

    const loadingToastId = toast.loading("Swapping tokens...");
    let currentOpId: string | null = null;

    const swapRouterAddress = import.meta.env.VITE_SWAP_ROUTER_CONTRACT_ADDRESS;

    const swapRouterContract = new SmartContract(
      connectedAccount,
      swapRouterAddress
    );

    try {
      console.log("swapping tokens...");

      console.log("Swap Route", swapRoute);
      console.log("Swap Router Address", swapRouterAddress);

      if (swapRoute.length === 0) {
        toast.error("No swap route available.");
        return;
      }

      let coins = Mas.fromString("0.02");

      // if (!swapRoute[0].token_in.is_native) {
      //   // Increase allownce for the swap
      //   const tokenContract = new MRC20(
      //     connectedAccount as any,
      //     swapRoute[0].token_in.address
      //   );

      //   // get the allowance of the swap router
      //   const allowance = await tokenContract.allowance(
      //     connectedAccount.address,
      //     swapRouterAddress
      //   );

      //   // We used fromAmount here instead of swapRoute[0].amount_in because we want to use the user input amount, which  is more precise than the amount_in due to the Number default rounding
      //   const amountIn = parseUnits(
      //     formatStringDecimals(fromAmount, fromToken.decimals),
      //     fromToken.decimals
      //   );

      //   console.log("amount in", amountIn);
      //   console.log("allowance", allowance);

      //   // if the allowance is less than the amount we want to swap, we need to increase it
      //   if (allowance < amountIn) {
      //     console.log(
      //       "allowance is less than the amount we want to swap, we need to increase it"
      //     );

      //     const tokenOperation = await tokenContract.increaseAllowance(
      //       swapRouterAddress,
      //       amountIn
      //     );

      //     const [, isSuccess] = await waitForExecution(tokenOperation);
      //     if (isSuccess) {
      //       console.log("Successfully increased allowance.");
      //     } else {
      //       console.log("Failed to increase allowance.");
      //       toast.update(loadingToastId, {
      //         render: "Failed to increase allowance.",
      //         type: "error",
      //         isLoading: false,
      //         autoClose: 5000,
      //       });
      //       setIsLoading(false);
      //       return;
      //     }
      //   }
      // } else {
      //   // increase coins
      //   coins += parseMas(formatStringDecimals(fromAmount, fromToken.decimals));
      // }

      const swapPaths = [];

      console.log(
        "Backend AmountIn Parsed",
        parseUnits(
          swapRoute[0].amount_in.toFixed(swapRoute[0].token_in.decimals + 1),
          swapRoute[0].token_in.decimals
        )
      );

      // Loop through the swap route and build the swap paths
      for (let i = 0; i < swapRoute.length; i++) {
        const receiverAddress =
          swapRoute[i].receiver_address === "caller"
            ? connectedAccount.address
            : swapRoute[i].receiver_address;

        const swapPath = new SwapPath(
          swapRoute[i].pool.pool_address,
          swapRoute[i].token_in.address,
          swapRoute[i].token_out.address,
          receiverAddress,
          // We use toFixed (with decimals + 1) to avoid rounding issues and scientific notations of the toString() method
          // We used fromAmount here instead of swapRoute[0].amount_in because we want to use the user input amount, which  is more precise than the amount_in due to the Number default rounding
          i == 0
            ? parseUnits(
                formatStringDecimals(fromAmount, fromToken.decimals),
                swapRoute[i].token_in.decimals
              )
            : parseUnits(
                swapRoute[i].amount_in.toFixed(
                  swapRoute[i].token_in.decimals + 1
                ),
                swapRoute[i].token_in.decimals
              ),
          parseUnits(
            swapRoute[i].min_amount_out.toFixed(
              swapRoute[i].token_out.decimals
            ),
            swapRoute[i].token_out.decimals
          ),
          swapRoute[i].is_transfer_from
        );

        swapPaths.push(swapPath);
      }

      let operation: Operation;

      const swapArgs = new Args()
        .addSerializableObjectArray(swapPaths)
        .addU64(Mas.fromString("0.01"))
        .addU64(BigInt(deadline));

      // If the token is native, add the amount to the coins and do normal swap without multi-call
      if (fromToken.is_native) {
        coins += parseMas(formatStringDecimals(fromAmount, fromToken.decimals));

        operation = await swapRouterContract.call(
          "swap",
          swapArgs.serialize(),
          {
            coins,
          }
        );
        currentOpId = operation.id;
        console.log(`Operation ID: ${operation.id}`);
      } else {
        // Increase allownce for the swap
        const tokenContract = new MRC20(connectedAccount, fromToken.address);

        // get the allowance of the swap router
        const allowance = await tokenContract.allowance(
          connectedAccount.address,
          swapRouterAddress
        );

        // We used fromAmount here instead of swapRoute[0].amount_in because we want to use the user input amount, which  is more precise than the amount_in due to the Number default rounding
        const amountIn = parseUnits(
          formatStringDecimals(fromAmount, fromToken.decimals),
          fromToken.decimals
        );

        console.log("amount in", amountIn);
        console.log("allowance", allowance);

        // if the allowance is greater or equal to the amount we want to swap, we do a nromal swap directly
        if (allowance >= amountIn) {
          console.log(
            "allowance is greater or equal to the amount we want to swap, we do a normal swap directly"
          );

          operation = await swapRouterContract.call(
            "swap",
            swapArgs.serialize(),
            {
              coins,
            }
          );
          currentOpId = operation.id;
          console.log(`Operation ID: ${operation.id}`);
        } else {
          // Merge the increaseAllowance calls and the swap call into a single operation
          const multicall = new Multicall(connectedAccount);

          const increaseAllowanceArgs = new Args()
            .addString(swapRouterAddress)
            .addU256(amountIn);

          const calls: Call[] = [
            {
              targetContract: fromToken.address,
              targetFunc: "increaseAllowance",
              callData: increaseAllowanceArgs.serialize(),
              coins: parseMas("0.02"),
            },
            {
              targetContract: swapRouterAddress,
              targetFunc: "swap",
              callData: swapArgs.serialize(),
              coins,
            },
          ];

          operation = await multicall.execute(calls, {
            maxGas: MAX_GAS_EXECUTE,
          });
        }
      }

      const [, isSuccess] = await waitForExecution(operation);

      if (isSuccess) {
        toast.update(loadingToastId, {
          render: (
            <div>
              {`Successfully swapped ${prettyPrice(
                Number(fromAmount),
                "USD",
                4,
                false,
                false
              )} ${fromToken.symbol} for ${prettyPrice(
                Number(toAmount),
                "USD",
                4,
                false,
                false
              )} ${toToken.symbol}.`}
              <a
                href={
                  network?.toUpperCase() === "BUILDNET"
                    ? `https://www.massexplo.com/tx/${operation.id}?network=buildnet`
                    : `https://explorer.massa.net/mainnet/operation/${operation.id}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="block mt-2 text-blue-600 hover:underline"
              >
                View on Explorer
              </a>
            </div>
          ),

          type: "success",
          isLoading: false,
          autoClose: 5000,
        });
        setIsLoading(false);
        pollBalances(3);
        setFromAmount("");
        setToAmount("");
        setPriceImpact(0);
        setFromUsd("0.00");
        setToUsd("0.00");
        setMinAmountOut(0);
      } else {
        const speculativeEvents = await operation.getSpeculativeEvents();
        const errorEvent = speculativeEvents.find((event) =>
          event.data.includes("massa_execution_error")
        );
        const errorMessage = errorEvent
          ? JSON.parse(errorEvent.data).massa_execution_error
          : "Unknown error occurred";
        logError(
          errorMessage,
          currentOpId,
          connectedAccount?.address || null
        ).catch((e) => console.error("Failed to log error", e));

        toast.update(loadingToastId, {
          render: (
            <div>
              Error: {errorMessage}
              <a
                href={
                  network?.toUpperCase() === "BUILDNET"
                    ? `https://www.massexplo.com/tx/${currentOpId}?network=buildnet`
                    : `https://explorer.massa.net/mainnet/operation/${currentOpId}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="block mt-2 text-blue-600 hover:underline"
              >
                View on Explorer
              </a>
            </div>
          ),
          type: "error",
          isLoading: false,
          autoClose: 5000,
        });
        setIsLoading(false);
      }

      //   const poolContract = new SmartContract(
      //     connectedAccount,
      //     bestPool.pool.pool_address
      //   );

      //   const minAmountOutParsed = parseUnits(
      //     minAmountOut.toString(),
      //     toToken.decimals
      //   );

      //   console.log("minAmountOut", minAmountOutParsed);

      //   const storageCosts = computeMintStorageCost(poolContract.address);

      //   const coins =
      //     Mas.fromString(fromAmount.toString()) +
      //     BigInt(storageCosts) +
      //     Mas.fromString("0.01");

      //   const coinsToSend = fromToken.is_native ? coins : Mas.fromString("0.01");

      //   const args = new Args()
      //     .addString(fromToken.address)
      //     .addU256(parseUnits(fromAmount.toString(), fromToken.decimals))
      //     .addU256(minAmountOutParsed);

      //   const swapWithMas = fromToken.is_native
      //     ? true
      //     : toToken.is_native
      //     ? true
      //     : false;

      //   if (!fromToken.is_native) {
      //     // Increase allownce for the swap
      //     const tokenContract = new MRC20(
      //       connectedAccount as any,
      //       fromToken.address
      //     );

      //     const tokenOperation = await tokenContract.increaseAllowance(
      //       poolContract.address,
      //       parseUnits(minAmountOutParsed.toString(), fromToken.decimals)
      //     );

      //     const tokenOperationStatus =
      //       await tokenOperation.waitSpeculativeExecution();

      //     if (tokenOperationStatus === OperationStatus.SpeculativeSuccess) {
      //       console.log("Successfully increased allowance.");
      //     } else {
      //       console.log("Failed to increase allowance.");

      //       toast.update(loadingToastId, {
      //         render: "Failed to increase allowance.",
      //         type: "error",
      //         isLoading: false,
      //         autoClose: 5000,
      //       });

      //       setIsLoading(false);

      //       return;
      //     }
      //   }

      //   const operation = await poolContract.call(
      //     swapWithMas ? "swapWithMas" : "swap",
      //     args.serialize(),
      //     {
      //       coins: coinsToSend,
      //     }
      //   );

      //   console.log(`Operation ID: ${operation.id}`);

      //   const status = await operation.waitSpeculativeExecution();
    } catch (error) {
      console.error("Error swapping tokens:", error);
      if (
        error instanceof Error &&
        error.message.includes("SLIPPAGE_LIMIT_EXCEEDED")
      ) {
        toast.update(loadingToastId, {
          render: "Slippage limit exceeded!",
          type: "error",
          isLoading: false,
          autoClose: 5000,
        });
      } else if (error instanceof Error) {
        if (error.message.includes("AmountIn must be greater than 0")) {
          toast.update(loadingToastId, {
            render: "Low amount in.",
            type: "error",
            isLoading: false,
            autoClose: 5000,
          });
        } else if (error.message.includes("insufficient balance")) {
          toast.update(loadingToastId, {
            render: "Insufficient balance. Decrease amount in.",
            type: "error",
            isLoading: false,
            autoClose: 5000,
          });
        } else {
          // toast.update(loadingToastId, {
          //   render: "An error occurred while swapping tokens.",
          //   type: "error",
          //   isLoading: false,
          //   autoClose: 5000,
          // });
          toast.update(loadingToastId, {
            render: error.message,
            type: "error",
            isLoading: false,
            autoClose: 5000,
          });
        }
      } else {
        toast.update(loadingToastId, {
          render: error as string,
          type: "error",
          isLoading: false,
          autoClose: 5000,
        });
      }

      setIsLoading(false);
    }
  };

  const fetchFromTokenBalance = useCallback(async () => {
    if (!connectedAccount || !fromToken) {
      setFromTokenBalance("0");
      return;
    }

    try {
      let balance: string;
      if (fromToken.is_native) {
        const masBalance = await connectedAccount.balance(false);

        console.log("masBalance", masBalance);

        balance = formatUnits(masBalance, fromToken.decimals);
      } else if (fromToken.address === MASSA_ROLLS_ADDRESS) {
        const client = new PublicAPI(PublicApiUrl.Buildnet);
        const addr_info: AddressInfo = await client.getAddressInfo(
          connectedAccount.address
        );

        balance = addr_info.candidate_roll_count.toString();
      } else {
        const tokenContract = new MRC20(connectedAccount, fromToken.address);

        const rawBalance = await tokenContract.balanceOf(
          connectedAccount.address
        );
        // const rawBalance = await connectedAccount.readStorage(
        //   fromToken.address,
        //   [connectedAccount.address].map((a) => `BALANCE${a}`),
        //   false
        // );
        // console.log("rowww balance", rawBalance);
        // if (!rawBalance || !rawBalance[0]) {
        //   console.warn("No balance found in storage for", fromToken.symbol);
        //   setFromTokenBalance(0);
        //   return;
        // }
        // let deserializedBalance: bigint = BigInt(0);
        // if (rawBalance[0]) {
        //   deserializedBalance = U256.fromBytes(rawBalance[0]);
        //   console.log("deserializedBalance", deserializedBalance);
        // } else {
        //   console.warn("rawBalance[0] is null");
        // }

        // console.log("rawBalance to logggg", rawBalance);
        // balance = Number(formatUnits(deserializedBalance, fromToken.decimals));
        console.log("rawBalance", rawBalance);
        balance = formatUnits(rawBalance, fromToken.decimals);
        console.log("balance", balance);
      }
      setFromTokenBalance(balance);
    } catch (error) {
      console.error("Error fetching fromToken balance:", error);
    }
  }, [connectedAccount, fromToken]);

  const fetchToTokenBalance = useCallback(async () => {
    if (!connectedAccount || !toToken) {
      setToTokenBalance("0");
      return;
    }

    try {
      let balance: string;
      if (toToken.is_native) {
        const masBalance = await connectedAccount.balance(false);
        balance = formatUnits(masBalance, toToken.decimals);
      } else {
        const tokenContract = new MRC20(connectedAccount, toToken.address);
        const rawBalance = await tokenContract.balanceOf(
          connectedAccount.address
        );
        balance = formatUnits(rawBalance, toToken.decimals);
        // const rawBalance = await connectedAccount.readStorage(
        //   toToken.address,
        //   [connectedAccount.address].map((a) => `BALANCE${a}`),
        //   false
        // );
        // if (!rawBalance || !rawBalance[0]) {
        //   console.warn("No balance found in storage for", fromToken.symbol);
        //   setToTokenBalance(0);
        //   return;
        // }
        // let deserializedBalance: bigint = BigInt(0);
        // if (rawBalance[0]) {
        //   deserializedBalance = U256.fromBytes(rawBalance[0]);
        //   console.log("deserializedBalance", deserializedBalance);
        // } else {
        //   console.warn("rawBalance[0] is null");
        // }

        // console.log("rawBalance to logggg", rawBalance);
        // balance = Number(formatUnits(deserializedBalance, toToken.decimals));
        // console.log("balance", balance);
      }
      setToTokenBalance(balance);
    } catch (error) {
      console.error("Error fetching toToken balance:", error);
    }
  }, [connectedAccount, toToken]);

  const fetchBalances = useCallback(async () => {
    if (!connectedAccount) return;

    try {
      await Promise.all([
        withRetry(fetchFromTokenBalance, 3),
        withRetry(fetchToTokenBalance, 3),
      ]);
    } catch (error) {
      console.error("Failed to fetch balances after retries:", error);
      toast.error("Failed to fetch balances. Trying again...");
    }
  }, [connectedAccount, fetchFromTokenBalance, fetchToTokenBalance]);
  const pollBalances = useCallback(
    async (maxAttempts: number) => {
      for (let attempt = 0; attempt < maxAttempts; attempt++) {
        await fetchBalances();
        await new Promise((resolve) => setTimeout(resolve, 3000));
      }
    },
    [fetchBalances]
  );

  useEffect(() => {
    if (!connectedAccount) return;

    fetchBalances();

    const interval = setInterval(fetchBalances, 15000);

    return () => clearInterval(interval);
  }, [connectedAccount, fetchBalances]);

  useEffect(() => {
    if (connectedAccount) {
      fetchBalances();
    } else {
      setFromTokenBalance("0");
      setToTokenBalance("0");
    }
  }, [fromToken, toToken, connectedAccount, fetchBalances]);

  let buttonText = "";
  let buttonDisabled = false;

  const hasLiquidity = bestPool !== null;
  const isNativeSwap =
    (fromToken?.symbol === "MAS" && toToken?.symbol === "WMAS") ||
    (fromToken?.symbol === "WMAS" && toToken?.symbol === "MAS");
  const parsedFromAmount = Number(fromAmount);
  const insufficientBalance = parsedFromAmount > Number(fromTokenBalance);
  const isMasToWmas = fromToken?.symbol === "MAS" && toToken?.symbol === "WMAS";
  const isWmasToMas = fromToken?.symbol === "WMAS" && toToken?.symbol === "MAS";
  const isBuyRolls = fromToken?.symbol === "MAS" && toToken?.symbol === "ROLL";
  const isSellRolls = fromToken?.symbol === "ROLL" && toToken?.symbol === "MAS";

  if (!connectedAccount) {
    buttonText = "Connect Wallet";
    buttonDisabled = false;
  } else if (isLoading) {
    buttonText = "Processing...";
    buttonDisabled = true;
  } else {
    if (!fromAmount || parsedFromAmount === 0) {
      buttonText = "Enter an amount";
      buttonDisabled = true;
    } else if (!hasLiquidity && !isNativeSwap && !isBuyRolls && !isSellRolls) {
      buttonText = "No Liquidity Available";
      buttonDisabled = true;
    } else if (insufficientBalance) {
      buttonText = "Insufficient Balance";
      buttonDisabled = true;
    } else {
      if (isBuyRolls) {
        buttonText = "Buy ROLLS";
      } else if (isSellRolls) {
        buttonText = "Sell ROLLS";
      } else if (isMasToWmas) {
        buttonText = "Wrap";
      } else if (isWmasToMas) {
        buttonText = "Unwrap";
      } else {
        buttonText = "Swap";
      }
      buttonDisabled = false;
    }
  }

  const handleButtonClick = () => {
    if (buttonDisabled) return;

    if (isBuyRolls) {
      handleBuyRoll();
    } else if (isSellRolls) {
      handleSellRoll();
    } else if (isMasToWmas) {
      handleWrapMAS();
    } else if (isWmasToMas) {
      handleUnwrapMAS();
    } else {
      handleSwapTokens();
    }
  };

  const handleBuyRoll = async () => {
    if (!connectedAccount) {
      toast.error("Please connect your wallet to buy ROLLS.");
      return;
    }

    const toastId = toast.loading("Buying ROLLS...");

    try {
      await buyRolls(connectedAccount, Number(toAmount));
      toast.update(toastId, {
        render: "Successfully bought ROLLS!",
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
      pollBalances(3);
      setFromAmount("");
      setToAmount("");
    } catch (error) {
      toast.update(toastId, {
        render: "Error buying ROLLS. Please try again.",
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
      return;
    }
  };

  const handleSellRoll = async () => {
    if (!connectedAccount) {
      toast.error("Please connect your wallet to sell ROLLS.");
      return;
    }

    const toastId = toast.loading("Selling ROLLS...");

    try {
      await sellRolls(connectedAccount, Number(fromAmount));
      toast.update(toastId, {
        render: "Successfully sold ROLLS!",
        type: "success",
        isLoading: false,
        autoClose: 3000,
      });
    } catch (error) {
      toast.update(toastId, {
        render: "Error selling ROLLS. Please try again.",
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
      return;
    }
  };

  const handleFromTokenSelect = (token: Token) => {
    if (fixFromToken) return;
    if (token.address === toToken.address) {
      toast.error("Cannot select the same token as 'To'");
      return;
    }
    setFromToken(token);
    setShowFromDropdown(false);

    if (token.symbol === "ROLL") {
      const masToken = liquidityTokens.find((t) => t.symbol === "MAS");
      if (masToken) {
        setToToken(masToken);
      }
    }

    if (fromAmount) {
      const convertedAmount =
        (Number(fromAmount) * token.price.usd) / toToken.price.usd;
      setToAmount(convertedAmount.toString());
    }
  };

  const handleToTokenSelect = (token: Token) => {
    if (fixToToken) return;
    if (token.address === fromToken.address) {
      toast.error("Cannot select the same token as 'From'");
      return;
    }
    setToToken(token);
    setShowToDropdown(false);

    if (token.symbol === "ROLL") {
      const masToken = liquidityTokens.find((t) => t.symbol === "MAS");
      if (masToken) {
        setFromToken(masToken);
      }
    }

    if (fromAmount) {
      const convertedAmount =
        (Number(fromAmount) * fromToken.price.usd) / token.price.usd;
      setToAmount(convertedAmount.toString());
    }
  };
  return (
    <>
      <ConnectWalletModal
        toggleConnectWalletModal={toggleConnectWalletModal}
        setToggleConnectWalletModal={setToggleConnectWalletModal}
      />
      <SettingsModal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        slippage={slippage}
        setSlippage={setSlippage}
      />
      <div
        className={`flex bg-[#F6F6F6] flex-col gap-3 p-4 md:p-6 pt-2 md:pt-3 rounded-xl border-2 border-[#000000] border-b-[8px] border-r-[6px]  md:rounded-xl  w-full h-full max-w-md md:max-w-lg mx-auto `}
      >
        {/* Settings Row */}
        <div className="flex justify-end items-center">
          <div className="flex items-center">
            <div className="flex flex-col text-right text-xs md:text-sm mr-1 font-medium">
              <span className={`text-xs ${themeStyles.textSecondary}`}>
                Slippage
              </span>
              <div
                className={`flex items-center justify-end text-xs ${themeStyles.textPrimary}`}
              >
                {slippage}%
              </div>
            </div>
            <button
              onClick={() => setShowSettingsModal(true)}
              className="p-1 md:p-2 hover:opacity-75 transition-opacity"
              aria-label="Settings"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 md:h-6 md:w-6 text-[#6e7aaa]"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </button>
          </div>
        </div>
        {/* From Section */}

        <div
          className={`rounded-lg p-4 border-2 ${themeStyles.inputBg} relative transition-all duration-200 hover:border-[#3F3F46]`}
        >
          <div className="flex justify-between mb-2">
            <div className="flex gap-2">
              <small className={`font-extrabold ${themeStyles.textSecondary}`}>
                From
              </small>
              <div className="text-xs flex items-center  [&_button]:text-gray-400 [&_button:hover]:text-blue-400">
                <AddressWithCopy
                  address={fromToken?.address}
                  shortenValue={4}
                  iconClassName="w-4 h-4"
                />
              </div>
            </div>
            {connectedAccount && (
              <small
                className={`cursor-pointer hover:underline ${themeStyles.textSecondary}`}
                onClick={() =>
                  handleFromAmountChange(
                    formatStringDecimals(fromTokenBalance, fromToken.decimals)
                  )
                }
              >
                Balance: {formatStringDecimals(fromTokenBalance)}
              </small>
            )}
          </div>
          <div className="flex items-center gap-2 md:gap-4">
            <div
              className="flex items-center gap-2 flex-1 min-w-0 cursor-pointer relative px-2 bg-black rounded-[50px]"
              onClick={() => setShowFromDropdown(!showFromDropdown)}
              ref={fromRef}
            >
              <div className="w-8 h-8 md:w-10 md:h-10 shrink-0 relative">
                {fromToken?.logo ? (
                  <TokenLogo
                    token={fromToken}
                    width="w-full"
                    height="h-full"
                    className="absolute inset-0 object-contain bg-black"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center rounded-full bg-slate-300 text-white text-sm md:text-lg font-semibold">
                    {fromToken?.symbol.slice(0, 3).toUpperCase()}
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0 overflow-hidden">
                <div className="flex items-center gap-1 justify-between w-full">
                  <div className="truncate flex-1 min-w-0">
                    <span className="block font-medium text-base md:text-lg text-white truncate">
                      {fromToken?.symbol}
                    </span>
                    <small className="text-white truncate block">
                      {fromToken?.name}
                    </small>
                  </div>
                  {!fixFromToken && (
                    <div className="flex-shrink-0 ml-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="white"
                        strokeWidth={4}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                  )}
                </div>
              </div>

              {/* From Dropdown */}
              {!fixFromToken && showFromDropdown && (
                <div
                  className={`absolute top-full mt-2 left-0 w-[150%] rounded-md shadow-lg z-50 ${themeStyles.dropdown} border-2 ${themeStyles.border} overflow-y-auto max-h-60`}
                >
                  <div className="sticky top-0 p-2 bg-inherit z-10 border-b border-black">
                    <input
                      type="text"
                      placeholder="Search name or address"
                      value={fromSearchQuery}
                      onChange={(e) => setFromSearchQuery(e.target.value)}
                      className={`w-full p-2 my-1 rounded-md  ${themeStyles.textInput} border border-black rounded-lg focus:outline-none`}
                      autoFocus
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                  {fromFilteredTokens.map((token) => (
                    <div
                      key={token.address}
                      onClick={() => handleFromTokenSelect(token)}
                      className={`flex items-center gap-3 p-2 md:p-3 cursor-pointer  ${themeStyles.dropdownItem} border-b border-b-[#1E1E1E] hover:bg-[#f1f5f9]`}
                    >
                      {token.logo ? (
                        <TokenLogo
                          token={token}
                          className="w-8 h-8 rounded-full object-contain"
                        />
                      ) : (
                        // <img
                        //   src={token.logo}
                        //   alt={token.symbol}
                        //   className="w-6 h-6 rounded-full object-contain"
                        // />
                        <div className="w-6 h-6 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold">
                          {token.symbol.slice(0, 3).toUpperCase()}
                        </div>
                      )}
                      <div className="truncate flex gap-1 justify-between w-full">
                        <div className="flex flex-col">
                          <span
                            className={`block font-medium ${themeStyles.textPrimary}`}
                          >
                            {token.symbol}
                          </span>
                          <small
                            className={`block ${themeStyles.textSecondary}`}
                          >
                            {token.name}
                          </small>
                        </div>
                        <small
                          className={`block ${themeStyles.textSecondary} text-xs opacity-75  flex items-center`}
                        >
                          {shortenAddress(token.address, 4)}
                        </small>
                      </div>
                    </div>
                  ))}
                  {fromFilteredTokens.length === 0 && (
                    <div className="p-2 text-center text-gray-500">
                      No tokens found
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="flex-1 min-w-6">
              <input
                ref={fromInputRef}
                autoFocus
                value={fromAmount}
                onChange={(e) => handleFromAmountChange(e.target.value)}
                className={`w-full bg-transparent text-black text-right text-xl md:text-2xl font-medium placeholder-gray-500 focus:outline-none ${themeStyles.textInput}`}
                placeholder="0.0"
              />
              <small
                className={`block text-right ${themeStyles.textSecondary}`}
              >
                ${fromUsd}
              </small>
            </div>
          </div>
        </div>
        {/* Swap Button */}
        <button
          onClick={handleSwap}
          className={`p-3 md:p-4   mx-auto -my-4 md:-my-7 z-10 transition-transform 
            
          
              
          ${isRotating ? "animate-spin-fast" : "hover:scale-105"}`}
          aria-label="Swap tokens"
        >
          <img
            src="/icons/swap icon2.svg"
            alt=""
            className="w-6 h-6 md:w-6 md:h-7"
          />
        </button>
        {/* To Section */}
        <div
          className={`rounded-lg p-4 border-2 ${themeStyles.inputBg} relative transition-all duration-200 hover:border-[#3F3F46]`}
        >
          <div className="flex justify-between mb-2">
            <div className="flex gap-2">
              <small className={`font-extrabold ${themeStyles.textSecondary}`}>
                To
              </small>
              <div className="text-xs flex items-center  [&_button]:text-gray-400 [&_button:hover]:text-blue-400">
                <AddressWithCopy
                  address={toToken?.address}
                  shortenValue={4}
                  iconClassName="w-4 h-4"
                />
              </div>
            </div>
            {connectedAccount && (
              <small
                className={`cursor-pointer hover:underline ${themeStyles.textSecondary}`}
                onClick={() =>
                  handleToAmountChange(
                    formatStringDecimals(toTokenBalance, toToken.decimals)
                  )
                }
              >
                Balance: {formatStringDecimals(toTokenBalance)}
              </small>
            )}
          </div>
          <div className="flex items-center gap-2 md:gap-4">
            <div
              className="flex items-center gap-2 flex-1 min-w-0 cursor-pointer relative px-2 bg-black rounded-[50px]"
              onClick={() => setShowToDropdown(!showToDropdown)}
              ref={toRef}
            >
              <div className="w-8 h-8 md:w-10 md:h-10 shrink-0 relative">
                {toToken?.logo ? (
                  <TokenLogo
                    token={toToken}
                    width="w-full"
                    height="h-full"
                    className="absolute inset-0 object-contain bg-black"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center rounded-full bg-slate-300 text-white text-sm md:text-lg font-semibold">
                    {toToken?.symbol.slice(0, 3).toUpperCase()}
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0 overflow-hidden">
                <div className="flex items-center gap-1 justify-between w-full">
                  <div className="truncate flex-1 min-w-0">
                    <span className="block font-medium text-base md:text-lg text-white truncate">
                      {toToken?.symbol}
                    </span>
                    <small className="text-white truncate block">
                      {toToken?.name}
                    </small>
                  </div>
                  {!fixToToken && (
                    <div className="flex-shrink-0 ml-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="white"
                        strokeWidth={4}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </div>
                  )}
                </div>
              </div>

              {!fixToToken && showToDropdown && (
                <div
                  className={`absolute top-full mt-2 left-0 w-[150%] rounded-md shadow-lg z-50 ${themeStyles.dropdown} border-2 ${themeStyles.border} overflow-y-auto max-h-60`}
                >
                  <div className="sticky top-0 p-2 bg-inherit z-10 border-b border-black">
                    <input
                      type="text"
                      placeholder="Search name or address"
                      value={toSearchQuery}
                      onChange={(e) => setToSearchQuery(e.target.value)}
                      className={`w-full p-2 my-1 rounded-md  ${themeStyles.textInput} border border-black rounded-lg focus:outline-none`}
                      autoFocus
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                  {toFilteredTokens.map((token) => (
                    <div
                      key={token.address}
                      onClick={() => handleToTokenSelect(token)}
                      className={`flex items-center gap-3 p-2 md:p-3 cursor-pointer border-b border-b-[#1E1E1E] hover:bg-[#f1f5f9] ${themeStyles.dropdownItem}`}
                    >
                      {token.logo ? (
                        <TokenLogo
                          token={token}
                          className="w-8 h-8 rounded-full object-contain"
                        />
                      ) : (
                        // <img
                        //   src={token.logo}
                        //   alt={token.symbol}
                        //   className="w-6 h-6 rounded-full object-contain"
                        // />
                        <div className="w-6 h-6 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold">
                          {token.symbol.slice(0, 3).toUpperCase()}
                        </div>
                      )}
                      <div className="truncate flex gap-1 justify-between w-full">
                        <div className="flex flex-col">
                          <span
                            className={`block font-medium ${themeStyles.textPrimary}`}
                          >
                            {token.symbol}
                          </span>
                          <small
                            className={`block ${themeStyles.textSecondary}`}
                          >
                            {token.name}
                          </small>
                        </div>
                        <small
                          className={`block ${themeStyles.textSecondary} text-xs opacity-75  flex items-center`}
                        >
                          {shortenAddress(token.address, 4)}
                        </small>
                      </div>
                    </div>
                  ))}
                  {toFilteredTokens.length === 0 && (
                    <div className="p-2 text-center text-gray-500">
                      No tokens found
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="flex-1 min-w-0">
              <input
                value={toAmount}
                onChange={(e) => handleToAmountChange(e.target.value)}
                className={`w-full bg-transparent text-black text-right text-xl md:text-2xl font-medium placeholder-gray-500 focus:outline-none ${themeStyles.textInput}`}
                placeholder="0.0"
              />
              <small
                className={`block text-right ${themeStyles.textSecondary}`}
              >
                ${toUsd}
              </small>
            </div>
          </div>
        </div>
        {/* Price Info */}
        <div className="space-y-2 text-sm md:text-base">
          <div className="flex justify-between">
            <span className={themeStyles.textSecondary}>Price Impact</span>
            <span className={themeStyles.textPrimary}>
              {priceImpact.toFixed(2)}%
            </span>
          </div>
          <div className="flex justify-between">
            <span className={themeStyles.textSecondary}>Minimum received</span>
            <span className={themeStyles.textPrimary}>
              {minAmountOut.toFixed(6)} {toToken?.symbol}
            </span>
          </div>
        </div>
        {/* Action Button */}
        {!connectedAccount ? (
          <button
            className={`w-full py-3 md:py-4 text-base md:text-lg font-semibold rounded-xl transition-opacity ${themeStyles.buttonBg} hover:opacity-90`}
            onClick={() => setToggleConnectWalletModal(true)}
          >
            Connect Wallet
          </button>
        ) : (
          <button
            className={`w-full py-3 md:py-2.5 text-base md:text-lg font-semibold rounded-xl transition-opacity ${
              buttonDisabled
                ? "bg-gray-400 text-white cursor-not-allowed"
                : `${themeStyles.buttonBg} hover:opacity-90`
            }`}
            disabled={buttonDisabled}
            onClick={handleButtonClick}
          >
            {buttonText}
          </button>
        )}
      </div>
    </>
  );
};

export default SwapWidget;
