import React, { useEffect, useState } from "react";
import { useAccountStore } from "@massalabs/react-ui-kit";
import axiosInstance from "../lib/axios/axiosInstance";
import { useNavigate } from "react-router-dom";
import LiquidityModal from "../components/LiquidityModal";
import { Pool as PoolsPool } from "./Pools";
import { formatDistanceToNow } from "date-fns";
// import { FiArrowDownRight, FiArrowUpRight } from "react-icons/fi";
import { useSearchParams } from "react-router-dom";
import AddressWithCopy from "../components/AddressWithCopy";
import TokenLogo from "../components/TokenLogo";
import { formatBalance } from "../lib/utils";
import { FiSearch } from "react-icons/fi";

export interface Token {
  address: string;
  circulated_supply: number;
  created_at: string;
  created_by: string;
  decimals: number;
  description: string;
  is_burnable: boolean;
  is_mintable: boolean;
  is_native: boolean;
  is_pausable: boolean;
  is_paused: boolean;
  logo: string;
  mas_price: number;
  name: string;
  price: {
    mas: number;
    usd: number;
  };
  status: string;
  symbol: string;
  total_supply: number;
  user_balance: number;
  user_balance_price: {
    mas: number;
    usd: number;
  };
}
interface Pool {
  balance: {
    a_amount: number;
    b_amount: number;
    lp_amount: string;
    price: {
      mas: number;
      usd: number;
    };
  };
  pool: {
    a_token: Token;
    b_token: Token;
    pool_address: string;
    a_reserve: number;
    b_reserve: number;
    total_lp_supply: string;
    user_lp_amount: string;
    input_fee: number;
    volume_24h: {
      mas: number;
      usd: number;
    };
    tvl: {
      mas: number;
      usd: number;
    };
    earned_fees_24h: {
      mas: number;
      usd: number;
    };
    apr: number;
  };
}
interface Staking {
  active_roll: null | any;
  roll: Token & {
    user_balance: number;
    user_balance_price: {
      mas: number;
      usd: number;
    };
  };
}

interface Transaction {
  timestamp: string;
  operation_id: string | null;
  a_amount: number;
  b_amount: number;
  action: string;
  a_token: {
    symbol: string;
    logo: string;
  };
  b_token: {
    symbol: string;
    logo: string;
  };
  is_a_token_in: boolean;
  is_b_token_in: boolean;
}

const SkeletonTokenRow = () => (
  <div className="flex justify-between items-center py-3 animate-pulse">
    <div className="flex gap-4 items-center">
      <div className="w-8 h-8 rounded-full bg-gray-300"></div>
      <div className="space-y-2">
        <div className="h-4 w-16 bg-gray-300 rounded"></div>
        <div className="h-3 w-24 bg-gray-300 rounded"></div>
      </div>
    </div>
    <div className="h-4 w-20 bg-gray-300 rounded"></div>
  </div>
);

const SkeletonPoolRow = () => (
  <div className="flex justify-between items-center py-3 animate-pulse">
    <div className="flex gap-4 items-center">
      <div className="flex -space-x-2">
        <div className="w-8 h-8 rounded-full bg-gray-300"></div>
        <div className="w-8 h-8 rounded-full bg-gray-300"></div>
      </div>
      <div className="space-y-2">
        <div className="h-4 w-24 bg-gray-300 rounded"></div>
        <div className="h-3 w-32 bg-gray-300 rounded"></div>
      </div>
    </div>
    <div className="h-4 w-20 bg-gray-300 rounded"></div>
  </div>
);

const SkeletonStaking = () => (
  <div className="space-y-4 animate-pulse">
    <div className="space-y-3">
      <div className="h-4 w-32 bg-gray-300 rounded"></div>
      <div className="h-6 w-24 bg-gray-300 rounded"></div>
    </div>
    <div className="space-y-3">
      <div className="h-4 w-32 bg-gray-300 rounded"></div>
      <div className="h-6 w-24 bg-gray-300 rounded"></div>
    </div>
  </div>
);

const SkeletonTransactionRow = () => (
  <tr className="animate-pulse">
    <td className="py-3 px-4">
      <div className="h-4 w-32 bg-gray-300 rounded"></div>
    </td>
    <td className="py-3 px-4">
      <div className="h-6 w-24 bg-gray-300 rounded"></div>
    </td>
    <td className="py-3 px-4">
      <div className="h-6 w-48 bg-gray-300 rounded"></div>
    </td>
    <td className="py-3 px-4">
      <div className="h-4 w-20 bg-gray-300 rounded"></div>
    </td>
  </tr>
);

const TokenDirection = ({
  amount,
  token,
}: // direction,
{
  amount: number;
  token: { symbol: string; logo: string };
  // direction: "in" | "out";
}) => {
  const hasLogo = token.logo && token.logo.trim() !== "";

  return (
    <div className="flex items-center gap-1">
      {hasLogo ? (
        <img
          src={token?.logo}
          alt={token?.symbol}
          className="w-5 h-5 rounded-full"
        />
      ) : (
        <div className="w-5 h-5 flex items-center justify-center text-[7px] bg-gray-200 rounded-full">
          {token?.symbol.slice(0, 3)}
        </div>
      )}
      <span className="font-medium">
        {amount.toFixed(4)} {token.symbol}
      </span>

      {/* {direction === "in" ? (
        <FiArrowUpRight className="text-green-600" />
      ) : (
        <FiArrowDownRight className="text-red-600" />
      )} */}
    </div>
  );
};

const Portfolio: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { connectedAccount, currentWallet } = useAccountStore();
  const targetAddress =
    searchParams.get("address") || connectedAccount?.address;
  const navigate = useNavigate();
  const [tokens, setTokens] = useState<Token[]>([]);
  const [pools, setPools] = useState<Pool[]>([]);
  const [stakings, setStakings] = useState<Staking | null>(null);
  const isOwnPortfolio = targetAddress === connectedAccount?.address;
  const [totalPortfolio, setTotalPortfolio] = useState<{
    mas: number;
    usd: number;
  }>({ mas: 0, usd: 0 });
  const [totalTokens, setTotalTokens] = useState<{ mas: number; usd: number }>({
    mas: 0,
    usd: 0,
  });
  const [totalPools, setTotalPools] = useState<{ mas: number; usd: number }>({
    mas: 0,
    usd: 0,
  });
  const [totalStakings, setTotalStakings] = useState<{
    mas: number;
    usd: number;
  }>({ mas: 0, usd: 0 });
  const [loading, setLoading] = useState(false);
  // const [error, setError] = useState<string | null>(null);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPool, setSelectedPool] = useState<PoolsPool | null>(null);
  const [initialMode, setInitialMode] = useState<"info" | "add">("info");

  const [transactions, setTransactions] = useState<Transaction[]>([]);
  // const [hasMoreTransactions, setHasMoreTransactions] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [transactionsLoading, setTransactionsLoading] = useState(false);
  const [transactionsError, setTransactionsError] = useState<string | null>(
    null
  );
  const [network, setNetwork] = useState<string | null>(null);

  const shouldAskToConnect = isOwnPortfolio && !connectedAccount;
  const [searchAddress, setSearchAddress] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchAddress.trim()) {
      navigate(`/portfolio?address=${searchAddress.trim()}`);
    }
  };

  useEffect(() => {
    const fetchNetworkInfo = async () => {
      if (connectedAccount && currentWallet) {
        const networkInfo = await currentWallet.networkInfos();
        const networkName = networkInfo?.name ?? null;
        setNetwork(networkName);
      } else {
        setNetwork(null);
      }
    };

    fetchNetworkInfo();

    // Listen for network changes
    const handleNetworkChange = async () => {
      await fetchNetworkInfo();
    };

    if (currentWallet) {
      currentWallet.listenNetworkChanges(handleNetworkChange);
    }
  }, [connectedAccount, currentWallet]);

  // Add transaction type styling
  const getActionStyle = (action: string) => {
    switch (action) {
      case "swap":
        return { bg: "bg-blue-100", textColor: "text-blue-800" };
      case "add_liquidity":
        return { bg: "bg-green-100", textColor: "text-green-800" };
      case "remove_liquidity":
        return { bg: "bg-red-100", textColor: "text-red-800" };
      case "create":
        return { bg: "bg-yellow-100", textColor: "text-yellow-800" };
      case "recover":
        return { bg: "bg-blue-100", textColor: "text-blue-800" };
      default:
        return { bg: "bg-gray-100", textColor: "text-gray-800" };
    }
  };
  useEffect(() => {
    setCurrentPage(1);
  }, [targetAddress]);
  // Add fetch transactions function
  const fetchTransactions = async () => {
    const addressToFetch = targetAddress || connectedAccount?.address;
    if (!addressToFetch) return;

    setTransactionsLoading(true);
    setTransactionsError(null);
    setTransactions([]);

    try {
      const limit = 10;
      const offset = (currentPage - 1) * limit;
      const response = await axiosInstance.get(
        `/users/${addressToFetch}/transactions`,
        { params: { limit, offset } }
      );

      const { data, total_pages } = response.data;
      const sortedData = [...data].sort((a, b) => {
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
      });
      setTransactions(sortedData);
      setTotalPages(total_pages);
    } catch (err) {
      setTransactionsError("Failed to load transactions");
      console.error("Error fetching transactions:", err);
    } finally {
      setTransactionsLoading(false);
    }
  };

  // Add useEffect for transactions
  useEffect(() => {
    fetchTransactions();
  }, [targetAddress, currentPage]);

  const handlePoolClick = (poolItem: Pool) => {
    const transformedPool: PoolsPool = {
      id: 0, // Dummy ID if not available
      poolName: `${poolItem.pool.a_token.symbol}/${poolItem.pool.b_token.symbol}`,
      a_token: poolItem.pool.a_token,
      b_token: poolItem.pool.b_token,
      a_reserve: poolItem.pool.a_reserve,
      b_reserve: poolItem.pool.b_reserve,
      total_lp_supply: poolItem.pool.total_lp_supply,
      logos: [poolItem.pool.a_token.logo, poolItem.pool.b_token.logo],
      tokenAddresses: [
        poolItem.pool.a_token.address,
        poolItem.pool.b_token.address,
      ],
      pool_address: poolItem.pool.pool_address,
      input_fee: `${poolItem.pool.input_fee}%`,
      volume_24h: poolItem.pool.volume_24h,
      tvl: poolItem.pool.tvl,
      earned_fees_24h: poolItem.pool.earned_fees_24h,
      apr: poolItem.pool.apr,
      user_lp_amount: parseFloat(poolItem.pool.user_lp_amount),
    };

    setSelectedPool(transformedPool);
    setInitialMode("info");
    setIsModalOpen(true);
  };

  const fetchPortfolioData = async () => {
    if (!targetAddress) return;

    setLoading(true);
    // setError(null);

    try {
      const response = await axiosInstance.get(
        `/users/${targetAddress}/portfolio`
      );
      const {
        tokens,
        pools,
        stakings,
        total_portfolio,
        total_tokens,
        total_pools,
        total_stakings,
      } = response.data;

      setTokens(tokens);
      setPools(pools);
      setStakings(stakings);
      setTotalPortfolio(total_portfolio);
      setTotalTokens(total_tokens);
      setTotalPools(total_pools);
      setTotalStakings(total_stakings);
    } catch (err) {
      // setError("Failed to fetch portfolio data");
      console.error("Error fetching portfolio:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log("transactions", transactions);
  }, [transactions]);

  useEffect(() => {
    fetchPortfolioData();
  }, [targetAddress]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  /* Old formatNumber function */
  // const formatNumber = (num: number, decimals: number = 4) => {
  //   return new Intl.NumberFormat("en-US", {
  //     minimumFractionDigits: decimals,
  //     maximumFractionDigits: decimals,
  //   }).format(num);
  // };

  // We used formatBalance function cause it roundes to the lowest unit
  const formatNumber = (num: number, decimals: number = 6) => {
    return formatBalance(num, decimals);
  };

  const renderTransactionsHistory = () => (
    <div>
      <div className="flex items-center justify-between mb-4">
        <span className="text-lg font-semibold text-gray-900">
          Transactions History
        </span>
      </div>

      {shouldAskToConnect ? (
        <div className="text-center py-6 text-gray-600">
          Connect your wallet to view transactions
        </div>
      ) : (
        <>
          {/* Desktop Table */}
          <div className="overflow-x-auto rounded-xl border-2 border-[#000000] border-b-[6px] border-r-[6px] hidden md:block">
            <table className="w-full table-auto text-sm bg-[#F6F6F6]">
              <thead className="bg-[#E6EBEC]">
                <tr>
                  <th className="py-3 px-4 text-left text-slate-900 font-semibold">
                    Time
                  </th>
                  <th className="py-3 px-4 text-left text-slate-900 font-semibold">
                    Type
                  </th>
                  <th className="py-3 px-4 text-left text-slate-900 font-semibold">
                    Amounts
                  </th>
                  <th className="py-3 px-4 text-left text-slate-900 font-semibold">
                    TX
                  </th>
                </tr>
              </thead>
              <tbody>
                {transactionsLoading
                  ? Array.from({ length: 10 }).map((_, i) => (
                      <SkeletonTransactionRow key={i} />
                    ))
                  : transactions.map((tx, i) => {
                      const actionStyle = getActionStyle(tx.action);
                      return (
                        <tr
                          key={i}
                          className="hover:bg-[#E6EBEC] border-b border-b-[#1E1E1E] ease-in-out duration-200 cursor-pointer"
                        >
                          <td className="py-3 px-4 text-slate-800">
                            {formatDistanceToNow(new Date(tx.timestamp), {
                              addSuffix: true,
                            })}
                          </td>
                          <td className="py-3 px-4">
                            <span
                              className={`px-2 py-1 rounded ${actionStyle.bg} ${actionStyle.textColor}`}
                            >
                              {tx.action.replace(/_/g, " ")}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-slate-800">
                            {tx.action === "swap" ? (
                              <div className="flex items-center gap-4">
                                <TokenDirection
                                  amount={
                                    tx.is_a_token_in ? tx.a_amount : tx.b_amount
                                  }
                                  token={
                                    tx.is_a_token_in ? tx.a_token : tx.b_token
                                  }
                                  // direction={tx.is_a_token_in ? "in" : "out"}
                                />
                                <TokenDirection
                                  amount={
                                    tx.is_a_token_in ? tx.b_amount : tx.a_amount
                                  }
                                  token={
                                    tx.is_a_token_in ? tx.b_token : tx.a_token
                                  }
                                  // direction={tx.is_b_token_in ? "in" : "out"}
                                />
                              </div>
                            ) : tx.action === "create" ? (
                              <div className="flex items-center gap-4">
                                {/* Show token logos without amounts for create action */}
                                <div className="flex items-center gap-1">
                                  {tx.a_token.logo ? (
                                    <img
                                      src={tx.a_token.logo}
                                      alt={tx.a_token.symbol}
                                      className="w-5 h-5 rounded-full"
                                    />
                                  ) : (
                                    <div className="w-5 h-5 flex items-center justify-center text-[7px] bg-gray-200 rounded-full">
                                      {tx.a_token.symbol.slice(0, 3)}
                                    </div>
                                  )}
                                  <span className="font-medium">
                                    {tx.a_token.symbol}
                                  </span>
                                </div>
                                <div className="flex items-center gap-1">
                                  {tx.b_token.logo ? (
                                    <img
                                      src={tx.b_token.logo}
                                      alt={tx.b_token.symbol}
                                      className="w-5 h-5 rounded-full"
                                    />
                                  ) : (
                                    <div className="w-5 h-5 flex items-center justify-center text-[7px] bg-gray-200 rounded-full">
                                      {tx.b_token.symbol.slice(0, 3)}
                                    </div>
                                  )}
                                  <span className="font-medium">
                                    {tx.b_token.symbol}
                                  </span>
                                </div>
                              </div>
                            ) : (
                              <div className="flex flex-col gap-2">
                                <TokenDirection
                                  amount={tx.a_amount}
                                  token={tx.a_token}
                                  // direction={
                                  //   tx.action === "add_liquidity" ? "in" : "out"
                                  // }
                                />
                                <TokenDirection
                                  amount={tx.b_amount}
                                  token={tx.b_token}
                                  // direction={
                                  //   tx.action === "add_liquidity" ? "in" : "out"
                                  // }
                                />
                              </div>
                            )}
                          </td>

                          {/* <td className="py-3 px-4">
                            <a
                              href={`https://explorer.massa.net/mainnet/operation/${tx.operation_id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {tx.operation_id.slice(0, 6)}...
                              {tx.operation_id.slice(-4)}
                            </a>
                          </td> */}
                          <td className="py-3 px-4">
                            <a
                              href={
                                network?.toUpperCase() === "BUILDNET"
                                  ? `https://www.massexplo.com/tx/${tx.operation_id}?network=buildnet`
                                  : `https://explorer.massa.net/mainnet/operation/${tx.operation_id}`
                              }
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              {tx.operation_id?.slice(0, 6)}...
                              {tx.operation_id?.slice(-4)}
                            </a>
                          </td>
                        </tr>
                      );
                    })}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="md:hidden space-y-4 mt-4">
            {transactionsLoading
              ? Array.from({ length: 10 }).map((_, i) => (
                  <div
                    key={i}
                    className="bg-[#F9FAFB] rounded-xl p-4 border-2 border-[#1E1E1E] animate-pulse"
                  >
                    <div className="space-y-3">
                      <div className="h-6 w-24 bg-gray-300 rounded"></div>
                      <div className="h-4 w-32 bg-gray-300 rounded"></div>
                      <div className="space-y-2">
                        <div className="h-4 w-full bg-gray-300 rounded"></div>
                        <div className="h-4 w-full bg-gray-300 rounded"></div>
                      </div>
                    </div>
                  </div>
                ))
              : transactions.map((tx, i) => {
                  const actionStyle = getActionStyle(tx.action);
                  return (
                    <div
                      key={i}
                      className="bg-[#F9FAFB] rounded-xl p-4 border-2 border-[#1E1E1E] hover:bg-[#E6EBEC] transition-colors"
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex flex-col">
                          <span
                            className={`px-2 py-1 rounded ${actionStyle.bg} ${actionStyle.textColor} mb-2 self-start`}
                          >
                            {tx.action.replace(/_/g, " ")}
                          </span>
                          <span className="text-sm text-gray-600">
                            {formatDistanceToNow(new Date(tx.timestamp), {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                        {/* <a
                          href={`https://explorer.massa.net/mainnet/operation/${tx.operation_id}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline text-sm"
                        >
                          View TX
                        </a> */}
                        <a
                          href={
                            network?.toUpperCase() === "BUILDNET"
                              ? `https://www.massexplo.com/tx/${tx.operation_id}?network=buildnet`
                              : `https://explorer.massa.net/mainnet/operation/${tx.operation_id}`
                          }
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline text-sm"
                        >
                          View TX
                        </a>
                      </div>
                      <div className="mt-2 text-sm text-gray-800">
                        {/* Token A */}
                        <div className="flex justify-between items-center mb-1">
                          <div className="flex items-center gap-1">
                            {tx.a_token.logo ? (
                              <img
                                src={tx.a_token.logo}
                                alt={tx.a_token.symbol}
                                className="w-5 h-5 rounded-full"
                              />
                            ) : (
                              <div className="w-5 h-5 flex items-center justify-center text-[7px] bg-gray-200 rounded-full">
                                {tx.a_token.symbol.slice(0, 3)}
                              </div>
                            )}
                            <span>{tx.a_token.symbol}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span>{tx.a_amount.toFixed(4)}</span>
                            {/* {tx.is_a_token_in ? (
                              <FiArrowUpRight className="text-green-600" />
                            ) : (
                              <FiArrowDownRight className="text-red-600" />
                            )} */}
                          </div>
                        </div>

                        {/* Token B */}
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-1">
                            {tx.b_token.logo ? (
                              <img
                                src={tx.b_token.logo}
                                alt={tx.b_token.symbol}
                                className="w-5 h-5 rounded-full"
                              />
                            ) : (
                              <div className="w-5 h-5 flex items-center justify-center text-[7px] bg-gray-200 rounded-full">
                                {tx.b_token.symbol.slice(0, 3)}
                              </div>
                            )}
                            <span>{tx.b_token.symbol}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span>{tx.b_amount.toFixed(4)}</span>
                            {/* {tx.is_b_token_in ? (
                              <FiArrowUpRight className="text-green-600" />
                            ) : (
                              <FiArrowDownRight className="text-red-600" />
                            )} */}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
          </div>

          {/* Pagination */}
          {transactions.length > 0 && (
            <div className="flex justify-center items-center gap-4 mt-6">
              <button
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                disabled={currentPage === 1 || transactionsLoading}
                className="px-4 py-2 rounded-md border-2 border-[#1E1E1E] bg-white hover:bg-[#E6EBEC] disabled:opacity-50"
              >
                Previous
              </button>
              <span className="text-slate-800">Page {currentPage}</span>
              <button
                onClick={() => setCurrentPage((p) => p + 1)}
                disabled={currentPage >= totalPages || transactionsLoading}
                className="px-4 py-2 rounded-md border-2 border-[#1E1E1E] bg-white hover:bg-[#E6EBEC] disabled:opacity-50"
              >
                Next
              </button>
            </div>
          )}

          {/* Loading State */}
          {/* {transactionsLoading && (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
            </div>
          )} */}

          {/* Empty State */}
          {!transactionsLoading && transactions.length === 0 && (
            <div className="text-center py-6 text-gray-600">
              No transactions found
            </div>
          )}

          {/* Error State */}
          {transactionsError && (
            <div className="text-center py-6 text-red-600 font-medium">
              {transactionsError}
            </div>
          )}
        </>
      )}
    </div>
  );

  return (
    <main className="container px-4 py-6 mx-auto md:px-6 lg:px-8">
      <div className="bg-white border-2 border-[#1E1E1E] rounded-3xl p-6 max-sm:p-3">
        {/* Header row */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          {/* Left: Title and Subtitle */}
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-1">
              {isOwnPortfolio ? "Your Portfolio" : "Portfolio"}
            </h2>
            <span className="text-sm text-gray-600">
              {isOwnPortfolio
                ? "Your Portfolio, Managed Precisely"
                : "Portfolio Overview"}
            </span>
          </div>
          {/* Right: Total Portfolio as Stat Card */}

          <div className="p-4 rounded-md bg-green-100 border-2 border-b-4 border-[#1E1E1E] shadow-sm flex flex-col items-center justify-center">
            <p className="text-gray-900 text-2xl font-semibold leading-tight">
              {formatCurrency(totalPortfolio?.usd)}
            </p>
            <p className="text-gray-600 text-lg">
              {totalPortfolio?.mas.toFixed(6)} MAS
            </p>
          </div>
          <div className="w-[213px]">
            {targetAddress && targetAddress !== connectedAccount?.address && (
              <div className="flex mb-4 justify-center">
                <span className="mr-1">Owner:</span>
                <AddressWithCopy address={targetAddress} shortenValue={4} />
              </div>
            )}
          </div>
        </div>

        {/*  Search Bar */}
        <div className="mb-6">
          <form
            onSubmit={handleSearch}
            className="flex gap-2 w-full max-w-2xl mr-auto"
          >
            <div className="relative flex-1">
              <input
                type="text"
                value={searchAddress}
                onChange={(e) => setSearchAddress(e.target.value)}
                placeholder="Enter an address"
                className="w-full pl-10 pr-28 py-3 rounded-lg border-2 border-[#1E1E1E] focus:outline-none focus:ring-1 focus:ring-slate-300"
              />
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-2">
                <button
                  type="submit"
                  className=" text-[#1E1E1E] px-3 py-1 rounded-md  hover:bg-gray-50 transition-colors text-sm"
                >
                  View
                </button>
                {/* {targetAddress &&
                  targetAddress !== connectedAccount?.address && (
                    <button
                      type="button"
                      onClick={() => {
                        navigate("/portfolio");
                        setSearchAddress("");
                      }}
                      className=" px-3 py-1 rounded-md border-2 border-[#1E1E1E] hover:bg-gray-300 transition-colors text-sm"
                    >
                      Clear
                    </button>
                  )} */}
              </div>
            </div>
          </form>
        </div>

        {/* Tokens + Pools + Staking side by side */}
        <div className="flex flex-col gap-6 mb-8 lg:flex-row">
          {/* Your Tokens */}
          <div className="p-4 rounded-xl bg-[#F9FAFB] border-2 border-[#1E1E1E] border-b-4 w-full lg:w-1/3">
            <div className="flex justify-between items-center mb-3">
              <span className="text-lg font-semibold text-gray-900">
                {isOwnPortfolio ? "Your Tokens" : "Tokens"}
              </span>
              <span className="text-lg font-semibold text-gray-900 bg-green-100 rounded-xl px-2 py-1">
                {formatCurrency(totalTokens.usd)}
              </span>
            </div>
            <div className="max-h-[250px] overflow-y-auto pr-2">
              {loading
                ? Array.from({ length: 3 }).map((_, i) => (
                    <SkeletonTokenRow key={i} />
                  ))
                : shouldAskToConnect
                ? shouldAskToConnect && (
                    <div className="flex flex-col justify-center items-center py-8 text-gray-600">
                      <span>Connect your wallet to view your assets.</span>
                    </div>
                  )
                : tokens &&
                  !loading &&
                  tokens
                    .slice()
                    .sort((a, b) => {
                      // Always put MAS first
                      if (a.symbol === "MAS") return -1;
                      if (b.symbol === "MAS") return 1;
                      if (a.symbol === "WMAS") return -1;
                      if (b.symbol === "WMAS") return 1;
                      return (
                        b.user_balance_price.usd - a.user_balance_price.usd
                      );
                    })
                    .map((token) => {
                      const dollarValue =
                        token.user_balance_price?.usd.toFixed(2);
                      const showLogo = token.logo && token.logo.trim() !== "";
                      const fallbackText = token.symbol
                        .slice(0, 3)
                        .toUpperCase();

                      return (
                        <div
                          key={token.address}
                          className="flex justify-between items-center py-3  transition-colors cursor-pointer"
                          onClick={() => navigate(`/token/${token.address}`)}
                        >
                          <div className="flex gap-4 items-center">
                            {showLogo ? (
                              <TokenLogo
                                token={token}
                                className="w-8 h-8"
                                width="32"
                                height="32"
                              />
                            ) : (
                              // <img
                              //   src={token.logo}
                              //   alt={token.symbol}
                              //   className="w-8 h-8 object-contain rounded-full"
                              // />
                              <div className="w-8 h-8 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold">
                                {fallbackText}
                              </div>
                            )}
                            <div>
                              <div className="text-base font-medium text-gray-900">
                                {token.symbol}
                              </div>
                              <div className="text-sm text-gray-600">
                                {formatNumber(token.user_balance)}
                              </div>
                            </div>
                          </div>
                          <span className="text-gray-900 font-medium">
                            ${dollarValue}
                          </span>
                        </div>
                      );
                    })}
            </div>
          </div>

          {/* Your Pools */}
          <div className="p-4 rounded-xl bg-[#F9FAFB] border-2 border-[#1E1E1E] border-b-4 w-full lg:w-1/3">
            <div className="flex justify-between items-center mb-3">
              <span className="text-lg font-semibold text-gray-900">
                {isOwnPortfolio ? "Your Pools" : "Pools"}
              </span>
              <span className="text-lg font-semibold text-gray-900 bg-green-100 rounded-xl px-2 py-1">
                {formatCurrency(totalPools.usd)}
              </span>
            </div>
            <div className="max-h-[250px] overflow-y-auto pr-2">
              {loading ? (
                Array.from({ length: 2 }).map((_, i) => (
                  <SkeletonPoolRow key={i} />
                ))
              ) : shouldAskToConnect ? (
                <div className="flex flex-col justify-center items-center py-8 text-gray-600">
                  <span>Connect your wallet to view your pools.</span>
                </div>
              ) : pools.length === 0 ? (
                isOwnPortfolio ? (
                  <div className="flex flex-col justify-center items-center py-8 text-gray-600">
                    <span>You don’t have any liquidity.</span>
                    <span className="text-sm mt-1">
                      Deposit liquidity into a pool to get started.
                    </span>
                  </div>
                ) : (
                  <div className="flex flex-col justify-center items-center py-8 text-gray-600">
                    <span>This address has no liquidity positions.</span>
                  </div>
                )
              ) : (
                pools.map((poolItem) => {
                  const aToken = poolItem.pool.a_token;
                  const bToken = poolItem.pool.b_token;

                  return (
                    <div
                      key={poolItem.pool.pool_address}
                      className="flex justify-between items-center py-3 transition-colors cursor-pointer"
                      onClick={() => handlePoolClick(poolItem)}
                    >
                      <div className="flex gap-4 items-center">
                        <div className="flex -space-x-2">
                          {aToken.logo ? (
                            <TokenLogo
                              token={aToken}
                              className="w-8 h-8"
                              width="32"
                              height="32"
                            />
                          ) : (
                            // <img
                            //   src={aToken.logo}
                            //   alt={aToken.symbol}
                            //   className="w-8 h-8 rounded-full object-cover border-2 border-white"
                            // />
                            <div className="w-8 h-8 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold border border-white">
                              {aToken.symbol.slice(0, 3)}
                            </div>
                          )}
                          {bToken.logo ? (
                            <img
                              src={bToken.logo}
                              alt={bToken.symbol}
                              className="w-8 h-8 rounded-full object-cover border-2 border-white"
                            />
                          ) : (
                            <div className="w-8 h-8 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold border border-white">
                              {bToken.symbol.slice(0, 3)}
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="text-base font-medium text-gray-900">
                            {aToken.symbol}-{bToken.symbol}
                          </div>
                          <div className="text-sm text-gray-600">
                            {formatNumber(poolItem.balance.a_amount)}{" "}
                            {aToken.symbol} +{" "}
                            {formatNumber(poolItem.balance.b_amount)}{" "}
                            {bToken.symbol}
                          </div>
                        </div>
                      </div>
                      <span className="text-gray-900 font-medium">
                        {formatCurrency(poolItem.balance.price.usd)}
                      </span>
                    </div>
                  );
                })
              )}
            </div>
          </div>

          {/* Your Staking */}
          <div className="p-4 rounded-xl bg-[#F9FAFB] border-2 border-[#1E1E1E] border-b-4 w-full lg:w-1/3">
            <div className="flex justify-between items-center mb-3">
              <span className="text-lg font-semibold text-gray-900">
                {isOwnPortfolio ? "Your Staking" : "Staking"}
              </span>
              <span className="text-lg font-semibold text-gray-900 bg-green-100 rounded-xl px-2 py-1">
                {formatCurrency(totalStakings.usd)}
              </span>
            </div>
            <div className="max-h-[250px] overflow-y-auto pr-2">
              {loading ? (
                <SkeletonStaking />
              ) : shouldAskToConnect ? (
                <div className="flex flex-col justify-center items-center py-8 text-gray-600">
                  <span>Connect your wallet to view staking</span>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Active Rolls */}
                  <div className=" p-3 rounded-xl">
                    <div className="text-sm font-medium text-gray-600  mb-2">
                      Active Rolls (Staking)
                    </div>
                    {stakings?.active_roll?.user_balance ? (
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <img
                            src={stakings.active_roll.logo}
                            alt="Active Rolls"
                            className="w-6 h-6"
                          />
                          <span className="text-gray-900">
                            {formatNumber(stakings.active_roll.user_balance)}
                          </span>
                        </div>
                        <span className="text-gray-900 font-medium">
                          {formatCurrency(
                            stakings.active_roll.user_balance_price.usd
                          )}
                        </span>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-600">
                        No active rolls participating in staking
                      </div>
                    )}
                  </div>

                  {/* Available Rolls */}
                  <div className="bg-gray-50 p-3 rounded-xl">
                    <div className="text-sm font-medium text-gray-600 mb-2">
                      Available Rolls
                    </div>
                    {stakings?.roll?.user_balance ? (
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <img
                            src={stakings.roll.logo}
                            alt="Available Rolls"
                            className="w-6 h-6"
                          />
                          <span className="text-gray-900">
                            {formatNumber(stakings.roll.user_balance)}
                          </span>
                        </div>
                        <span className="text-gray-900 font-medium">
                          {formatCurrency(stakings.roll.user_balance_price.usd)}
                        </span>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-600">
                        No rolls available in wallet
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Transactions History */}
        {renderTransactionsHistory()}
      </div>
      {isModalOpen && selectedPool && (
        <LiquidityModal
          pool={selectedPool}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedPool(null);
          }}
          isConnected={!!connectedAccount}
          initialMode={initialMode}
        />
      )}
    </main>
  );
};

export default Portfolio;
