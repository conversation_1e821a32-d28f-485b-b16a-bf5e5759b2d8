import React, { createContext, useContext, useState, useEffect } from "react";
import UnderMaintenance from "../components/UnderMaintenance";

interface MaintenanceContextType {
  isInMaintenance: boolean;
  setMaintenanceMode: (
    active: boolean,
    options?: {
      message?: string;
      expectedTime?: string;
      allowNavigation?: boolean;
    }
  ) => void;
}

const MaintenanceContext = createContext<MaintenanceContextType>({
  isInMaintenance: false,
  setMaintenanceMode: () => {},
});

export const useMaintenanceMode = () => useContext(MaintenanceContext);

export const MaintenanceProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // Read maintenance mode from environment variable
  const [isInMaintenance, setIsInMaintenance] = useState(
    import.meta.env.VITE_MAINTENANCE_MODE === "true"
  );

  const [options, setOptions] = useState({
    message:
      import.meta.env.VITE_MAINTENANCE_MESSAGE ||
      "We're currently under maintenance",
    expectedTime:
      import.meta.env.VITE_MAINTENANCE_EXPECTED_TIME ||
      "Please check back soon",
    allowNavigation:
      import.meta.env.VITE_MAINTENANCE_ALLOW_NAVIGATION === "true",
  });

  // Update maintenance mode if env variable changes (for development)
  useEffect(() => {
    const checkMaintenanceMode = () => {
      setIsInMaintenance(import.meta.env.VITE_MAINTENANCE_MODE === "true");
    };

    window.addEventListener("focus", checkMaintenanceMode);
    return () => window.removeEventListener("focus", checkMaintenanceMode);
  }, []);

  const setMaintenanceMode = (active: boolean, newOptions = {}) => {
    setIsInMaintenance(active);
    if (newOptions) {
      setOptions({ ...options, ...newOptions });
    }
  };

  return (
    <MaintenanceContext.Provider
      value={{ isInMaintenance, setMaintenanceMode }}
    >
      {children}
      {isInMaintenance && (
        <UnderMaintenance
          isOverlay={false}
          message={options.message}
          expectedTime={options.expectedTime}
          allowNavigation={options.allowNavigation}
        />
      )}
    </MaintenanceContext.Provider>
  );
};
