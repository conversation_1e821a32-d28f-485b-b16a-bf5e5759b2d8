import React, { useEffect, useRef, useState, useCallback } from "react";
import { useAccountStore } from "@massalabs/react-ui-kit";
import { Pool } from "../pages/Pools";
import {
  MRC20,
  SmartContract,
  parseUnits,
  formatUnits,
  Operation,
  Multicall,
  Args,
  Call,
  parseMas,
  MAX_GAS_EXECUTE,
} from "@massalabs/massa-web3";
import {
  addLiquidity,
  addLiquidityWithMAS,
  removeLiquidity,
} from "../services/Liquidity";
import {
  computeMintStorageCost,
  formatBalance,
  formatStringDecimals,
  NATIVE_MAS_COIN_ADDRESS,
  truncateDecimals,
  waitForExecution,
} from "../lib/utils";
import { toast } from "react-toastify";
// import { LP_DECIMALS } from "../lib/data";
import axiosInstance from "../lib/axios/axiosInstance";
import AnimatedNumber from "./AnimatedNumber";
import AddressWithCopy from "./AddressWithCopy";
import { useNavigate } from "react-router-dom";
import TokenLogo from "./TokenLogo";
import { logError, prettyPrice } from "../lib/utils2";

interface TokenDetails {
  address: string;
  symbol: string;
  logo: string;
  decimals: number;
  price: number;
  balance: string;
}
interface LiquidityModalProps {
  pool: Pool;
  onClose: () => void;
  initialMode?: "info" | "add" | "remove";
  isConnected: boolean;
  // onRequestConnect: () => void;
}

const LiquidityModal: React.FC<LiquidityModalProps> = ({
  pool,
  onClose,
  initialMode = "info",
  isConnected,
  // onRequestConnect,
}) => {
  const { connectedAccount, currentWallet } = useAccountStore();
  const [network, setNetwork] = useState<string | null>(null);

  const masToken: TokenDetails = {
    address: NATIVE_MAS_COIN_ADDRESS,
    symbol: "MAS",
    logo: "/images/massa.jpg",
    decimals: 9,
    price: 1,
    balance: "0",
  };

  const availableTokensForB: TokenDetails[] = [
    {
      address: pool.b_token.address,
      symbol: pool.b_token.symbol,
      logo: pool.b_token.logo,
      decimals: pool.b_token.decimals,
      price: pool.b_token.mas_price,
      balance: "0",
    },
    masToken,
  ];

  const [mode, setMode] = useState<"info" | "add" | "remove">(initialMode);
  const [aAmount, setAAmount] = useState("");
  const [bAmount, setBAmount] = useState("");
  const [removePercentage, setRemovePercentage] = useState(0);
  const [lpBalance, setLpBalance] = useState(0n);
  // const [loadingPoolData, setLoadingPoolData] = useState(false);
  const [loadingBalances, setLoadingBalances] = useState(false);
  const [reserveA, setReserveA] = useState(0);
  const [reserveB, setReserveB] = useState(0);
  const [totalLpSupply, setTotalLpSupply] = useState(0n);
  const [poolShare, setPoolShare] = useState(0);
  const [estimatedLPAmount, setEstimatedLPAmount] = useState("");
  const [masPriceInUSD, setMasPriceInUSD] = useState(0);

  useEffect(() => {
    // Save original body style
    const originalStyle = window.getComputedStyle(document.body).overflow;

    // Don't prevent scrolling on mobile
    const isMobile = window.innerWidth <= 768;
    if (!isMobile) {
      document.body.style.overflow = "hidden";
    }

    // Cleanup function to restore original style
    return () => {
      document.body.style.overflow = originalStyle;
    };
  }, []);

  useEffect(() => {
    const fetchNetworkInfo = async () => {
      if (connectedAccount && currentWallet) {
        const networkInfo = await currentWallet.networkInfos();
        const networkName = networkInfo?.name ?? null;
        setNetwork(networkName);
      } else {
        setNetwork(null);
      }
    };

    fetchNetworkInfo();

    const handleNetworkChange = async () => {
      await fetchNetworkInfo();
    };

    if (currentWallet) {
      currentWallet.listenNetworkChanges(handleNetworkChange);
    }
  }, [connectedAccount, currentWallet]);

  useEffect(() => {
    const fetchMasPrice = async () => {
      try {
        const response = await axiosInstance.get("/tokens/NATIVE_COIN/price");
        setMasPriceInUSD(response.data.usd);
      } catch (error) {
        console.error("Error fetching MAS price:", error);
        toast.error("Failed to fetch MAS price");
      }
    };
    fetchMasPrice();
  }, []);
  const [tokenA, setTokenA] = useState<TokenDetails>({
    address: pool.a_token.address,
    symbol: pool.a_token.symbol,
    logo: pool.a_token.logo,
    decimals: pool.a_token.decimals,
    price: pool.a_token.mas_price,
    balance: "0",
  });
  const [tokenB, setTokenB] = useState<TokenDetails>({
    address: pool.b_token.address,
    symbol: pool.b_token.symbol,
    logo: pool.b_token.logo,
    decimals: pool.b_token.decimals,
    price: pool.b_token.mas_price,
    balance: "0",
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [showToDropdown, setShowToDropdown] = useState(false);
  const toRef = useRef<HTMLDivElement>(null);
  const [selectedTokenB, setSelectedTokenB] = useState<TokenDetails>(tokenB);
  const [dataVersion, setDataVersion] = useState(0);
  const navigate = useNavigate();

  const getExplorerUrl = (opId: string) => {
    return network?.toUpperCase() === "BUILDNET"
      ? `https://www.massexplo.com/tx/${opId}?network=buildnet`
      : `https://explorer.massa.net/mainnet/operation/${opId}`;
  };

  const fetchPoolData = useCallback(async () => {
    if (!pool?.pool_address) return;

    try {
      const poolPromise = axiosInstance.get(`/pools/${pool.pool_address}`);

      const userPromise = connectedAccount
        ? axiosInstance.get(
            `/users/${connectedAccount.address}/pool/${pool.pool_address}/lp`
          )
        : Promise.resolve({ data: { amount: "0" } });

      const [poolResponse, userResponse] = await Promise.all([
        poolPromise,
        userPromise,
      ]);
      const data = poolResponse.data;
      setReserveA(data.a_reserve);
      setReserveB(data.b_reserve);

      setTokenA((prev) => ({
        ...prev,
        address: data.a_token.address,
        symbol: data.a_token.symbol,
        logo: data.a_token.logo,
        decimals: data.a_token.decimals,
        price: data.a_token.mas_price,
      }));
      setTokenB((prev) => ({
        ...prev,
        address: data.b_token.address,
        symbol: data.b_token.symbol,
        logo: data.b_token.logo,
        decimals: data.b_token.decimals,
        price: data.b_token.mas_price,
        balance: prev.balance,
      }));

      const userLp = BigInt(userResponse.data.amount || 0);
      const totalLpSupply = BigInt(data.total_lp_supply || 0);
      const poolShare =
        totalLpSupply > 0n ? (Number(userLp) / Number(totalLpSupply)) * 100 : 0;

      setLpBalance(userLp);
      setTotalLpSupply(totalLpSupply);
      setPoolShare(poolShare);

      setDataVersion((v) => v + 1);
    } catch (error) {
      console.error("Error fetching pool data:", error);
      toast.error("Failed to fetch pool data");
    }
  }, [pool?.pool_address, connectedAccount]);
  const fetchBalance = async (
    tokenAddress: string,
    decimals: number
  ): Promise<string> => {
    if (!connectedAccount) return "0";
    try {
      if (tokenAddress === NATIVE_MAS_COIN_ADDRESS) {
        const masBalanceBigInt = await connectedAccount.balance(true);
        return formatUnits(masBalanceBigInt, decimals);
      } else {
        const tokenContract = new MRC20(connectedAccount, tokenAddress);
        const rawBalance = await tokenContract.balanceOf(
          connectedAccount.address
        );
        console.log(
          "raw balance with format unit ",
          formatUnits(rawBalance, decimals)
        );
        console.log("raw balance ", rawBalance);

        return formatUnits(rawBalance, decimals);
      }
    } catch (error) {
      console.error(`Error fetching balance for ${tokenAddress}:`, error);
      return "0";
    }
  };
  const fetchTokenBalances = useCallback(async () => {
    if (!connectedAccount) return;
    setLoadingBalances(true);
    try {
      const [aBalance, bBalance] = await Promise.all([
        fetchBalance(tokenA.address, tokenA.decimals),
        fetchBalance(selectedTokenB.address, selectedTokenB.decimals),
      ]);

      setTokenA((prev) => ({ ...prev, balance: aBalance }));
      setSelectedTokenB((prev) => ({ ...prev, balance: bBalance }));
    } catch (error) {
      console.error("Error fetching token balances:", error);
      toast.error("Failed to fetch balances");
    } finally {
      setLoadingBalances(false);
    }
  }, [
    connectedAccount,
    tokenA.address,
    tokenA.decimals,
    selectedTokenB.address,
    selectedTokenB.decimals,
  ]);
  useEffect(() => {
    fetchPoolData();
    fetchTokenBalances();
  }, [fetchPoolData, fetchTokenBalances]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      fetchPoolData();
      fetchTokenBalances();
    }, 10000);
    return () => clearInterval(intervalId);
  }, [fetchPoolData, fetchTokenBalances]);

  // Utility function to fetch token balance

  // // Fetch Token A balance
  // useEffect(() => {
  //   const fetchTokenABalance = async () => {
  //     if (!connectedAccount || !tokenA.address || tokenA.decimals === undefined)
  //       return;
  //     setLoadingBalances(true);
  //     const balance = await fetchBalance(tokenA.address, tokenA.decimals);
  //     setTokenA((prev) => ({ ...prev, balance }));
  //     setLoadingBalances(false);
  //   };
  //   fetchTokenABalance();
  // }, [connectedAccount, tokenA.address, tokenA.decimals]);

  // // Fetch Selected Token B balance
  // useEffect(() => {
  //   const fetchSelectedTokenBBalance = async () => {
  //     if (
  //       !connectedAccount ||
  //       !selectedTokenB?.address ||
  //       selectedTokenB?.decimals === undefined
  //     )
  //       return;
  //     setLoadingBalances(true);
  //     const balance = await fetchBalance(
  //       selectedTokenB.address,
  //       selectedTokenB.decimals
  //     );
  //     setSelectedTokenB((prev) => ({ ...prev, balance }));
  //     setLoadingBalances(false);
  //   };
  //   fetchSelectedTokenBBalance();
  // }, [connectedAccount, selectedTokenB?.address, selectedTokenB?.decimals]);

  // Update selectedTokenB when pool changes
  // useEffect(() => {
  //   if (selectedTokenB?.address !== tokenB.address) {
  //     setSelectedTokenB(tokenB);
  //   }
  // }, [tokenB, selectedTokenB?.address]);

  // Click outside handler for dropdown
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (toRef.current && !toRef.current.contains(e.target as Node)) {
        setShowToDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const getEstimatedLPAmount = () => {
    if (aAmount && bAmount && tokenA && selectedTokenB) {
      try {
        const parsedAAmount = parseUnits(aAmount, tokenA.decimals);
        const parsedReserveA = parseUnits(
          reserveA.toFixed(tokenA.decimals + 1),
          tokenA.decimals
        );
        if (parsedReserveA === 0n) {
          setEstimatedLPAmount("0");
          return;
        }
        const estimatedLP = (parsedAAmount * totalLpSupply) / parsedReserveA;
        const lpDecimals = Math.max(tokenA.decimals, selectedTokenB.decimals);
        setEstimatedLPAmount(formatUnits(estimatedLP, lpDecimals));
      } catch (error) {
        console.error("Error estimating LP:", error);
        setEstimatedLPAmount("0");
      }
    }
  };

  useEffect(() => {
    getEstimatedLPAmount();
  }, [aAmount, bAmount, reserveA, tokenA, totalLpSupply]);

  const insufficientABalance =
    tokenA &&
    parseUnits(aAmount || "0", tokenA.decimals) >
      parseUnits(tokenA.balance, tokenA.decimals);

  const insufficientBBalance =
    selectedTokenB &&
    parseUnits(bAmount || "0", selectedTokenB.decimals) >
      parseUnits(selectedTokenB.balance, selectedTokenB.decimals);
  const isAddDisabled =
    !aAmount ||
    !bAmount ||
    insufficientABalance ||
    insufficientBBalance ||
    !selectedTokenB;

  const calculateUSDValue = (amount: string, price?: number) =>
    (parseFloat(amount || "0") * (price || 0)).toFixed(2);

  const handleAddLiquidity = async () => {
    if (!connectedAccount || !tokenA || !selectedTokenB) return;
    if (
      !aAmount ||
      !bAmount ||
      parseFloat(aAmount) <= 0 ||
      parseFloat(bAmount) <= 0
    )
      return;

    setIsProcessing(true);
    const toastId = toast.loading("Processing transaction...");

    try {
      const poolContract = new SmartContract(
        connectedAccount as any,
        pool.pool_address
      );

      const isUsingMAS = selectedTokenB.address === NATIVE_MAS_COIN_ADDRESS;

      console.log("aAmount", aAmount);
      console.log("bAmount", bAmount);

      const formattedAAmount = formatStringDecimals(aAmount, tokenA.decimals);
      const formattedBAmount = formatStringDecimals(
        bAmount,
        selectedTokenB.decimals
      );

      console.log("formattedAAmount", formattedAAmount);
      console.log("formattedBAmount", formattedBAmount);

      const parsedAAmount = parseUnits(formattedAAmount, tokenA.decimals);
      const parsedBAmount = parseUnits(
        formattedBAmount,
        selectedTokenB.decimals
      );

      console.log("parsedAAmount", parsedAAmount);
      console.log("parsedBAmount", parsedBAmount);

      // if (tokenA.address !== NATIVE_MAS_COIN_ADDRESS) {
      //   const tokenAContract = new MRC20(
      //     connectedAccount as any,
      //     tokenA.address
      //   );
      //   const allowance = await tokenAContract.allowance(
      //     connectedAccount.address,
      //     pool.pool_address
      //   );
      //   console.log("allowanceA", allowance);
      //   console.log("parsedAAmount to allow", parsedAAmount);
      //   if (allowance < parsedAAmount) {
      //     const operation = await tokenAContract.increaseAllowance(
      //       pool.pool_address,
      //       parsedAAmount,
      //       {
      //         coins: Mas.fromString("0.1"),
      //       }
      //     );

      //     const [, isSuccess] = await waitForExecution(operation);

      //     if (isSuccess) {
      //       toast.success(
      //         `Allowance for ${tokenA.symbol} increased successfully`
      //       );
      //     } else {
      //       toast.error("Allowance increase failed");
      //       return;
      //     }
      //   }
      // }

      // if (selectedTokenB.address !== NATIVE_MAS_COIN_ADDRESS && !isUsingMAS) {
      //   const tokenBContract = new MRC20(
      //     connectedAccount as any,
      //     selectedTokenB.address
      //   );
      //   const allowance = await tokenBContract.allowance(
      //     connectedAccount.address,
      //     pool.pool_address
      //   );
      //   if (allowance < parsedBAmount) {
      //     const operation = await tokenBContract.increaseAllowance(
      //       pool.pool_address,
      //       parsedBAmount,
      //       {
      //         coins: Mas.fromString("0.1"),
      //       }
      //     );

      //     const [, isSuccess] = await waitForExecution(operation);

      //     if (isSuccess) {
      //       toast.success(
      //         `Allowance for ${selectedTokenB.symbol} increased successfully`
      //       );
      //     } else {
      //       toast.error("Allowance increase failed");
      //       return;
      //     }
      //   }
      // }

      let operation: Operation;

      if (isUsingMAS) {
        let isAAllownaceCall = false;

        // Get token A allowance
        const tokenAContract = new MRC20(
          connectedAccount as any,
          tokenA.address
        );

        const aAllowance = await tokenAContract.allowance(
          connectedAccount.address,
          pool.pool_address
        );

        console.log("allowanceA", aAllowance);
        console.log("parsedAAmount to allow", parsedAAmount);

        // Check if allowance is not enough to add liquidity
        if (aAllowance < parsedAAmount) {
          isAAllownaceCall = true;
        }

        if (isAAllownaceCall) {
          // Do a multicall to increase allowance and add liquidityWithMas
          const multicall = new Multicall(connectedAccount);

          const storageCosts = computeMintStorageCost(poolContract.address);
          const liqCoins =
            parsedBAmount + BigInt(storageCosts) + parseMas("0.1");

          const increaseAAllowanceArgs = new Args()
            .addString(pool.pool_address)
            .addU256(parsedAAmount);

          const calls: Call[] = [
            {
              targetContract: tokenAContract.address,
              targetFunc: "increaseAllowance",
              callData: increaseAAllowanceArgs.serialize(),
              coins: parseMas("0.02"),
            },
            {
              targetContract: poolContract.address,
              targetFunc: "addLiquidityWithMas",
              callData: new Args()
                .addU256(parsedAAmount)
                .addU256(parsedBAmount)
                .addU256(0n)
                .addU256(0n)
                .serialize(),
              coins: liqCoins,
            },
          ];

          operation = await multicall.execute(calls, {
            maxGas: MAX_GAS_EXECUTE,
          });
        } else {
          // Do a single call to add liquidityWithMas
          operation = await addLiquidityWithMAS(
            poolContract,
            parsedAAmount,
            parsedBAmount,
            0n,
            0n
          );
        }
      } else {
        let isAAllownaceCall = false;
        let isBAllownaceCall = false;

        // Get token A allowance
        const tokenAContract = new MRC20(
          connectedAccount as any,
          tokenA.address
        );

        const aAllowance = await tokenAContract.allowance(
          connectedAccount.address,
          pool.pool_address
        );

        console.log("allowanceA", aAllowance);

        // Get token B allowance
        const tokenBContract = new MRC20(
          connectedAccount as any,
          selectedTokenB.address
        );

        const bAllowance = await tokenBContract.allowance(
          connectedAccount.address,
          pool.pool_address
        );

        console.log("allowanceB", bAllowance);

        // Check if allowance is not enough to add liquidity
        if (aAllowance < parsedAAmount) {
          isAAllownaceCall = true;
        }

        if (bAllowance < parsedBAmount) {
          isBAllownaceCall = true;
        }

        if (!isAAllownaceCall && !isBAllownaceCall) {
          operation = await addLiquidity(
            poolContract,
            parsedAAmount,
            parsedBAmount,
            0n,
            0n
          );
        } else {
          const multicall = new Multicall(connectedAccount);

          const calls: Call[] = [];

          if (isAAllownaceCall) {
            const increaseAAllowanceArgs = new Args()
              .addString(pool.pool_address)
              .addU256(parsedAAmount);

            calls.push({
              targetContract: tokenAContract.address,
              targetFunc: "increaseAllowance",
              callData: increaseAAllowanceArgs.serialize(),
              coins: parseMas("0.02"),
            });
          }

          if (isBAllownaceCall) {
            const increaseBAllowanceArgs = new Args()
              .addString(pool.pool_address)
              .addU256(parsedBAmount);

            calls.push({
              targetContract: tokenBContract.address,
              targetFunc: "increaseAllowance",
              callData: increaseBAllowanceArgs.serialize(),
              coins: parseMas("0.02"),
            });
          }

          // Add the addLiquidity call
          calls.push({
            targetContract: poolContract.address,
            targetFunc: "addLiquidity",
            callData: new Args()
              .addU256(parsedAAmount)
              .addU256(parsedBAmount)
              .addU256(0n)
              .addU256(0n)
              .serialize(),
            coins: parseMas("0.1"),
          });

          console.log("calls", calls);

          operation = await multicall.execute(calls, {
            maxGas: MAX_GAS_EXECUTE,
          });
        }
      }
      const opId = operation.id;

      const [, isSuccess] = await waitForExecution(operation);

      if (isSuccess) {
        toast.dismiss();
        toast.success(
          <div>
            Liquidity added successfully!
            <a
              href={getExplorerUrl(opId)}
              target="_blank"
              rel="noopener noreferrer"
              className="block mt-2 text-blue-600 hover:underline"
            >
              View on Explorer
            </a>
          </div>
        );
        setAAmount("");
        setBAmount("");
        fetchPoolData();
        fetchTokenBalances();
      } else {
        const speculativeEvents = await operation.getSpeculativeEvents();
        const errorEvent = speculativeEvents.find((event) =>
          event.data.includes("massa_execution_error")
        );
        const errorMessage = errorEvent
          ? JSON.parse(errorEvent.data).massa_execution_error
          : "Unknown error occurred";

        logError(errorMessage, opId, connectedAccount?.address || null).catch(
          (e) => console.error("Failed to log error", e)
        );

        toast.dismiss();
        toast.error(
          <div>
            Error: {errorMessage}
            <a
              href={getExplorerUrl(opId)}
              target="_blank"
              rel="noopener noreferrer"
              className="block mt-2 text-blue-600 hover:underline"
            >
              View on Explorer
            </a>
          </div>
        );
      }
    } catch (error) {
      console.error("Add liquidity error:", error);
      toast.update(toastId, {
        render: error instanceof Error ? error.message : (error as string),
        type: "error",
        isLoading: false,
        autoClose: 5000,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRemoveLiquidity = async () => {
    if (!connectedAccount || !tokenA || !tokenB) return;
    setIsProcessing(true);
    const toastId = toast.loading("Processing transaction...");

    try {
      const lpAmount = (lpBalance * BigInt(removePercentage)) / 100n;
      const poolContract = new SmartContract(
        connectedAccount as any,
        pool.pool_address
      );
      // await removeLiquidity(
      //   poolContract,
      //   lpAmount,
      //   0,
      //   0,
      //   tokenA.decimals,
      //   tokenB.decimals
      // );
      // toast.update(toastId, {
      //   render: "Liquidity removed successfully!",
      //   type: "success",
      //   isLoading: false,
      //   autoClose: 5000,
      // });
      // fetchPoolData();
      // fetchTokenBalances();
      const operation = await removeLiquidity(
        poolContract,
        lpAmount,
        0,
        0,
        tokenA.decimals,
        tokenB.decimals
      );

      const opId = operation.id;

      const [, isSuccess] = await waitForExecution(operation);

      if (isSuccess) {
        toast.dismiss();
        toast.success(
          <div>
            Liquidity removed successfully!
            <a
              href={getExplorerUrl(opId)}
              target="_blank"
              rel="noopener noreferrer"
              className="block mt-2 text-blue-600 hover:underline"
            >
              View on Explorer
            </a>
          </div>
        );
        fetchPoolData();
        fetchTokenBalances();
      } else {
        const speculativeEvents = await operation.getSpeculativeEvents();
        const errorEvent = speculativeEvents.find((event) =>
          event.data.includes("massa_execution_error")
        );
        const errorMessage = errorEvent
          ? JSON.parse(errorEvent.data).massa_execution_error
          : "Unknown error occurred";

        logError(errorMessage, opId, connectedAccount?.address || null).catch(
          (e) => console.error("Failed to log error", e)
        );

        toast.dismiss();
        toast.error(
          <div>
            Error: {errorMessage}
            <a
              href={getExplorerUrl(opId)}
              target="_blank"
              rel="noopener noreferrer"
              className="block mt-2 text-blue-600 hover:underline"
            >
              View on Explorer
            </a>
          </div>
        );
      }
    } catch (error) {
      console.error("Remove liquidity error:", error);
      toast.update(toastId, {
        render:
          error instanceof Error ? error.message : "Failed to remove liquidity",
        type: "error",
        isLoading: false,
        autoClose: 5000,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const renderInfoContent = () => {
    const totalLiquidityInMAS =
      reserveA * (tokenA?.price || 0) + reserveB * (tokenB?.price || 0);
    const userShare =
      totalLpSupply > 0n ? Number(lpBalance) / Number(totalLpSupply) : 0;
    const lpUSDValue = userShare * totalLiquidityInMAS * masPriceInUSD;
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa] p-3 ">
            <div className="text-sm text-gray-600 mb-2">Total Value Locked</div>
            <div className="text-lg font-semibold text-slate-700">
              <AnimatedNumber
                value={pool.tvl.usd}
                format={(v) =>
                  `$${v?.toLocaleString(undefined, {
                    maximumFractionDigits: 2,
                  })}`
                }
                dataVersion={dataVersion}
              />
            </div>
          </div>

          <div className=" p-3  border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa]">
            <div className="text-sm text-gray-600 mb-2">Volume (24h)</div>
            <div className="text-lg font-semibold text-slate-700">
              <AnimatedNumber
                value={pool.volume_24h.usd}
                format={(v) =>
                  `$${v.toLocaleString(undefined, {
                    maximumFractionDigits: 2,
                  })}`
                }
                dataVersion={dataVersion}
              />
            </div>
          </div>

          <div className="p-3  border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa]">
            <div className="text-sm text-gray-600 mb-2">Fees (24h)</div>
            <div className="text-lg font-semibold text-slate-700">
              <AnimatedNumber
                value={pool.earned_fees_24h.usd}
                format={(v) =>
                  `$${v.toLocaleString(undefined, {
                    maximumFractionDigits: 2,
                  })}`
                }
                dataVersion={dataVersion}
              />
            </div>
          </div>

          <div className="p-3  border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa]">
            <div className="text-sm text-gray-600 mb-2">APR</div>
            <div className="text-lg font-semibold text-slate-700">
              <AnimatedNumber
                value={pool.apr}
                format={(v) => `${v.toFixed(2)}%`}
                dataVersion={dataVersion}
              />
            </div>
          </div>
        </div>

        <div className="p-3  border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa]">
          <h4 className="text-lg font-semibold text-slate-700 mb-3">
            Your Position
          </h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-gray-600">LP Tokens</div>
              <div className="text-base font-medium text-slate-700">
                <AnimatedNumber
                  value={Number(
                    formatUnits(
                      lpBalance,
                      Math.max(tokenA.decimals, tokenB.decimals)
                    )
                  )}
                  format={(v) => v.toFixed(4)}
                  dataVersion={dataVersion}
                />
              </div>
            </div>
            <div>
              {/* <div className="text-sm text-gray-600">Pool Share</div>
            <div className="text-base font-medium text-slate-700">
              <AnimatedNumber
                value={poolShare}
                format={(v) => `${v.toFixed(2)}%`}
                dataVersion={dataVersion}
              />
            </div>
          </div> */}
              <div className="text-sm text-gray-600">LP Value</div>
              <div className="text-base font-medium text-slate-700">
                <AnimatedNumber
                  value={lpUSDValue}
                  format={(v) => `$${v.toFixed(2)}`}
                  dataVersion={dataVersion}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderTabs = () => (
    <div className="flex p-6 pb-0 font-semibold">
      {isConnected && (
        <>
          <button
            onClick={() => setMode("info")}
            className={`flex-1 py-2 rounded-l-xl border-2 border-[#1E1E1E] transition-all ${
              mode === "info"
                ? "bg-[#F6C955] text-[#333333] border-b-4 border-r-4"
                : "bg-[#E5E7EB] text-[#4A5568] hover:bg-[#FDE68A]"
            }`}
          >
            Info
          </button>

          <button
            onClick={() => setMode("add")}
            className={`flex-1 py-2 border-2 border-[#1E1E1E] border-l-0 transition-all ${
              mode === "add"
                ? "bg-[#F6C955] text-[#333333] border-b-4"
                : "bg-[#E5E7EB] text-[#4A5568] hover:bg-[#FDE68A]"
            }`}
          >
            Add
          </button>
          <button
            onClick={() => setMode("remove")}
            className={`flex-1 py-2 rounded-r-xl border-2 border-[#1E1E1E] transition-all ${
              mode === "remove"
                ? "bg-[#F6C955] text-[#333333] border-b-4 border-l-4"
                : "bg-[#E5E7EB] text-[#4A5568] hover:bg-[#FDE68A]"
            }`}
          >
            Remove
          </button>
        </>
      )}
    </div>
  );

  // Guest sees only general metrics + connect CTA
  const renderGuestInfo = () => (
    <div className="p-6 space-y-6">
      {/* reuse your general metric cards */}

      <div className="grid grid-cols-2 gap-4">
        <div className="rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa] p-3 ">
          <div className="text-sm text-gray-600 mb-2">Total Value Locked</div>
          <div className="text-lg font-semibold text-slate-700">
            <AnimatedNumber
              value={pool.tvl.usd}
              format={(v) =>
                `$${v?.toLocaleString(undefined, {
                  maximumFractionDigits: 2,
                })}`
              }
              dataVersion={dataVersion}
            />
          </div>
        </div>

        <div className=" p-3  border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa]">
          <div className="text-sm text-gray-600 mb-2">Volume (24h)</div>
          <div className="text-lg font-semibold text-slate-700">
            <AnimatedNumber
              value={pool.volume_24h.usd}
              format={(v) =>
                `$${v.toLocaleString(undefined, {
                  maximumFractionDigits: 2,
                })}`
              }
              dataVersion={dataVersion}
            />
          </div>
        </div>

        <div className="p-3  border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa]">
          <div className="text-sm text-gray-600 mb-2">Fees (24h)</div>
          <div className="text-lg font-semibold text-slate-700">
            <AnimatedNumber
              value={pool.earned_fees_24h.usd}
              format={(v) =>
                `$${v.toLocaleString(undefined, {
                  maximumFractionDigits: 2,
                })}`
              }
              dataVersion={dataVersion}
            />
          </div>
        </div>

        <div className="p-3  border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa]">
          <div className="text-sm text-gray-600 mb-2">APR</div>
          <div className="text-lg font-semibold text-slate-700">
            <AnimatedNumber
              value={pool.apr}
              format={(v) => `${v.toFixed(2)}%`}
              dataVersion={dataVersion}
            />
          </div>
        </div>
      </div>

      <div className="text-center mt-6">
        <p className="mb-4 text-gray-700">
          Connect your wallet to view your position, add or remove liquidity.
        </p>
        {/* <button
          onClick={onRequestConnect}
          className="bg-[#F6C955] px-6 py-2 rounded-lg font-semibold border-2 border-[#1E1E1E] hover:bg-[#FDE68A]"
        >
          Connect Wallet
        </button> */}
      </div>
    </div>
  );

  const BalanceDisplay = ({ value, decimals = 4, suffix = "" }: any) => (
    <AnimatedNumber
      value={parseFloat(value || "0")}
      format={(v) => `${formatBalance(v, decimals)}${suffix}`}
      dataVersion={dataVersion}
    />
  );

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[59]"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl w-full max-w-lg border-2 border-[#1E1E1E] border-b-4 border-r-4 shadow-xl z-[60]"
        style={{ maxHeight: "98vh", overflowY: "auto" }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center pb-4 p-6 border-b-2 border-[#1E1E1E]">
          <div>
            <div className="flex">
              <h2 className="text-2xl font-bold text-[#1E1E1E] mb-1">
                {pool.poolName} Pool
              </h2>
            </div>

            {/* Price Display */}
            {tokenA && tokenB && (
              <div className="mt-2 flex items-center gap-2">
                <span className="text-sm text-gray-500 font-normal">
                  Current price:
                </span>
                <span className="bg-blue-50 px-2 py-0.5 rounded text-sm font-semibold text-blue-700">
                  1 {tokenA.symbol} ={" "}
                  {tokenA.price
                    ? prettyPrice(
                        Number(reserveB / reserveA),
                        "USD",
                        4,
                        true,
                        false
                      )
                    : "–"}{" "}
                  {tokenB.symbol}
                </span>
              </div>
            )}

            {/* Pool Address */}
            <div className="mt-2 flex items-center gap-2">
              <span className="text-sm text-gray-500 font-normal">
                Pool Address:
              </span>
              <span className="text-sm text-gray-700 font-medium">
                <AddressWithCopy address={pool.pool_address} />
              </span>
            </div>
          </div>

          {/* Token Logos & Close Button */}
          <div className="flex flex-col">
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-right"
            >
              ✕
            </button>
            <div className="flex -space-x-2">
              {pool.logos.map((logo, index) => {
                const token = index === 0 ? pool.a_token : pool.b_token;
                if (!logo) {
                  return (
                    <div
                      key={index}
                      className="w-10 h-10 rounded-full bg-slate-300 flex items-center justify-center border-2 border-white max-sm:w-7 max-sm:h-7"
                    >
                      <span className="text-xs font-medium text-white">
                        {token.symbol.slice(0, 5)}
                      </span>
                    </div>
                  );
                }
                return (
                  <TokenLogo
                    key={index}
                    token={token}
                    className="w-10 h-10 rounded-full border-2 border-white max-sm:w-7 max-sm:h-7"
                    width="w-10"
                    height="h-10"
                  />
                );
              })}
            </div>
          </div>
        </div>
        <div className="p-6 pb-0">
          {connectedAccount && (
            <div className="grid grid-cols-2 gap-4">
              <div className="p-3 rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa]">
                <div className="text-sm text-[#4A5568]">Total Liquidity</div>
                <div className="text-base font-semibold text-[#1E1E1E]">
                  <AnimatedNumber
                    value={reserveB * 2 * masPriceInUSD}
                    format={(v) => `$${v.toFixed(2)}`}
                    dataVersion={dataVersion}
                  />
                </div>
              </div>

              <div className="p-3 rounded-lg border-2 bg-[#E6EBEC] text-[#6e7aaa]">
                <div className="text-sm text-[#4A5568]">Your Pool Share</div>
                <div className="text-base font-semibold text-[#1E1E1E]">
                  <AnimatedNumber
                    value={poolShare}
                    format={(v) => `${v.toFixed(2)}%`}
                    dataVersion={dataVersion}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="p-6 pb-0 pt-3">
          <div className="grid grid-cols-2 gap-4">
            <div
              className={`flex items-center gap-3 p-3 border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] cursor-pointer `}
              onClick={() => {
                navigate(`/token/${tokenA.address}`);
              }}
            >
              {tokenA?.logo ? (
                <TokenLogo
                  token={tokenA}
                  className="w-8 h-8 rounded-full"
                  height="h-9"
                  width="w-9"
                />
              ) : (
                // <img
                //   src={tokenA.logo}
                //   alt={tokenA.symbol}
                //   className="w-8 h-8 rounded-full"
                // />
                <div className="w-8 h-8 rounded-full bg-slate-300 flex items-center justify-center">
                  <span className="text-xs font-medium text-white">
                    {tokenA?.symbol.slice(0, 5)}
                  </span>
                </div>
              )}
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">{tokenA?.symbol}</span>
                </div>
                <div className="text-sm text-gray-600">
                  <AnimatedNumber
                    value={reserveA}
                    format={(v) => v.toFixed(2)}
                    dataVersion={dataVersion}
                  />
                </div>
              </div>
            </div>
            <div
              className={`flex items-center gap-3 p-3   border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] cursor-pointer `}
              onClick={() => {
                navigate(`/token/${tokenB.address}`);
              }}
            >
              {tokenB?.logo ? (
                <img
                  src={tokenB.logo}
                  alt={tokenB.symbol}
                  className="w-8 h-8 rounded-full"
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-slate-300 flex items-center justify-center">
                  <span className="text-xs font-medium text-white">
                    {tokenB?.symbol.slice(0, 3)}
                  </span>
                </div>
              )}
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">{tokenB?.symbol}</span>
                </div>
                <div className="text-sm text-gray-600">
                  <AnimatedNumber
                    value={reserveB}
                    format={(v) => v.toFixed(2)}
                    dataVersion={dataVersion}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        {renderTabs()}
        {mode === "info" && !isConnected && renderGuestInfo()}
        {mode === "info" && isConnected && (
          <div className="p-6">{renderInfoContent()}</div>
        )}

        <div className="p-6">
          {mode === "add" && isConnected && (
            <>
              <div className="space-y-4">
                <div className="p-3  border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] ">
                  <div className="flex justify-between mb-2">
                    <span className="text-sm text-gray-600">
                      Price:{" "}
                      {/* {(tokenA?.price * masPriceInUSD)?.toFixed(4) || "0.0000"} */}
                      {tokenA?.price && masPriceInUSD
                        ? prettyPrice(tokenA.price * masPriceInUSD, "USD")
                        : "0.0000"}
                    </span>
                    <span
                      className="text-sm text-gray-600 cursor-pointer hover:underline"
                      onClick={() => {
                        if (!loadingBalances && tokenA?.balance) {
                          try {
                            const parsedBalance = parseUnits(
                              tokenA.balance,
                              tokenA.decimals
                            );
                            setAAmount(tokenA.balance);

                            if (reserveA > 0 && reserveB > 0) {
                              // Calculate expectedB using BigInt to prevent precision loss
                              const reserveABN = parseUnits(
                                reserveA.toFixed(tokenA.decimals + 1),
                                tokenA.decimals
                              );
                              const reserveBBN = parseUnits(
                                reserveB.toFixed(selectedTokenB.decimals + 1),
                                selectedTokenB.decimals
                              );
                              const expectedBBN =
                                (parsedBalance * reserveBBN) / reserveABN;
                              setBAmount(
                                formatUnits(
                                  expectedBBN,
                                  selectedTokenB.decimals
                                )
                              );
                            } else if (
                              tokenA.price > 0 &&
                              selectedTokenB.price > 0
                            ) {
                              // Calculate based on prices using precise decimal handling
                              const parsedPriceA = parseUnits(
                                tokenA.price.toFixed(tokenA.decimals + 1),
                                tokenA.decimals
                              );
                              const parsedPriceB = parseUnits(
                                selectedTokenB.price.toFixed(
                                  selectedTokenB.decimals + 1
                                ),
                                selectedTokenB.decimals
                              );
                              const expectedBBN =
                                (parsedBalance * parsedPriceA) / parsedPriceB;
                              setBAmount(
                                formatUnits(
                                  expectedBBN,
                                  selectedTokenB.decimals
                                )
                              );
                            } else {
                              setBAmount("");
                            }
                          } catch (error) {
                            console.error(
                              "Error calculating expectedB:",
                              error
                            );
                            toast.error("Failed to calculate expected amount");
                          }
                        }
                      }}
                    >
                      Balance: <BalanceDisplay value={tokenA?.balance} />
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <input
                      type="number"
                      value={aAmount}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (Number(value) < 0) return;
                        setAAmount(value);
                        if (value) {
                          if (reserveA > 0 && reserveB > 0) {
                            const expectedB =
                              (reserveB / reserveA) * Number(value);
                            setBAmount(
                              truncateDecimals(
                                expectedB,
                                selectedTokenB.decimals
                              )
                            );
                          } else if (
                            tokenA.price > 0 &&
                            selectedTokenB.price > 0
                          ) {
                            const expectedB =
                              (Number(value) * tokenA.price) /
                              selectedTokenB.price;
                            setBAmount(
                              expectedB.toFixed(selectedTokenB.decimals)
                            );
                          } else {
                            setBAmount("");
                          }
                        } else {
                          setBAmount("");
                        }
                      }}
                      placeholder="0.0"
                      className="flex-1 bg-transparent text-xl focus:outline-none"
                    />
                    <div className="flex items-center gap-2">
                      {tokenA?.logo ? (
                        <TokenLogo
                          token={tokenA}
                          className="w-6 h-6 rounded-full"
                          height="h-7"
                          width="w-7"
                        />
                      ) : (
                        // <img
                        //   src={tokenA.logo}
                        //   alt={tokenA.symbol}
                        //   className="w-6 h-6 rounded-full"
                        // />
                        <div className="w-6 h-6 rounded-full bg-slate-300 text-white text-xs font-semibold flex items-center justify-center">
                          {pool.a_token.symbol.slice(0, 3)}
                        </div>
                      )}
                      <span className="font-medium">{pool.a_token.symbol}</span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    ${calculateUSDValue(aAmount, tokenA?.price * masPriceInUSD)}
                  </div>
                  {insufficientABalance && (
                    <p className="text-red-500 text-sm mt-1">
                      Insufficient balance
                    </p>
                  )}
                </div>
                <div className="p-3  border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC] ">
                  <div className="flex justify-between mb-2">
                    <span className="text-sm text-gray-600">
                      Price: $
                      {(selectedTokenB?.price * masPriceInUSD)?.toFixed(4) ||
                        "0.0000"}
                    </span>
                    <span
                      className="text-sm text-gray-600 cursor-pointer hover:underline"
                      onClick={() => {
                        if (!loadingBalances && selectedTokenB?.balance) {
                          try {
                            const parsedBalance = parseUnits(
                              selectedTokenB.balance,
                              selectedTokenB.decimals
                            );
                            setBAmount(selectedTokenB.balance);

                            if (reserveA > 0 && reserveB > 0) {
                              const reserveABN = parseUnits(
                                reserveA.toFixed(tokenA.decimals + 1),
                                tokenA.decimals
                              );
                              const reserveBBN = parseUnits(
                                reserveB.toFixed(selectedTokenB.decimals + 1),
                                selectedTokenB.decimals
                              );
                              const expectedAAN =
                                (parsedBalance * reserveABN) / reserveBBN;
                              setAAmount(
                                formatUnits(expectedAAN, tokenA.decimals)
                              );
                            } else if (
                              tokenA.price > 0 &&
                              selectedTokenB.price > 0
                            ) {
                              const parsedPriceA = parseUnits(
                                tokenA.price.toFixed(tokenA.decimals + 1),
                                tokenA.decimals
                              );
                              const parsedPriceB = parseUnits(
                                selectedTokenB.price.toFixed(
                                  selectedTokenB.decimals + 1
                                ),
                                selectedTokenB.decimals
                              );
                              const expectedAAN =
                                (parsedBalance * parsedPriceB) / parsedPriceA;
                              setAAmount(
                                formatUnits(expectedAAN, tokenA.decimals)
                              );
                            } else {
                              setAAmount("");
                            }
                          } catch (error) {
                            console.error(
                              "Error calculating expectedA:",
                              error
                            );
                            toast.error("Failed to calculate expected amount");
                          }
                        }
                      }}
                    >
                      Balance:{" "}
                      <BalanceDisplay value={selectedTokenB?.balance} />
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <input
                      type="number"
                      value={bAmount}
                      onChange={(e) => {
                        const value = e.target.value;
                        if (Number(value) < 0) return;
                        setBAmount(value);
                        if (value) {
                          if (reserveA > 0 && reserveB > 0) {
                            const expectedA =
                              (reserveA / reserveB) * Number(value);
                            setAAmount(
                              truncateDecimals(expectedA, tokenA.decimals)
                            );
                          } else if (
                            tokenA.price > 0 &&
                            selectedTokenB.price > 0
                          ) {
                            const expectedA =
                              (Number(value) * selectedTokenB.price) /
                              tokenA.price;
                            setAAmount(
                              truncateDecimals(expectedA, tokenA.decimals)
                            );
                          } else {
                            setAAmount("");
                          }
                        } else {
                          setAAmount("");
                        }
                      }}
                      placeholder="0.0"
                      className="flex-1 bg-transparent text-xl focus:outline-none"
                    />
                    <div
                      className="flex items-center gap-2 relative"
                      ref={toRef}
                    >
                      <button
                        onClick={() => setShowToDropdown(!showToDropdown)}
                        className="flex items-center gap-2 focus:outline-none"
                      >
                        {selectedTokenB?.logo ? (
                          <img
                            src={selectedTokenB.logo}
                            alt={selectedTokenB.symbol}
                            className="w-6 h-6 rounded-full"
                          />
                        ) : (
                          <div className="w-6 h-6 rounded-full bg-slate-300 text-white text-xs font-semibold flex items-center justify-center">
                            {selectedTokenB?.symbol.slice(0, 3)}
                          </div>
                        )}
                        <span className="font-medium">
                          {selectedTokenB?.symbol}
                        </span>
                        <svg
                          className="w-4 h-4 text-gray-600"
                          fill="none"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                      {showToDropdown && (
                        <div className="absolute top-full mt-2 right-0 w-[140px] bg-white border-2 border-black rounded-md shadow-lg z-50">
                          {availableTokensForB.map((token) => (
                            <div
                              key={token.address}
                              onClick={() => {
                                setSelectedTokenB(token);
                                setShowToDropdown(false);
                                // if (aAmount) {
                                //   if (tokenA.price > 0 && token.price > 0) {
                                //     const expectedB =
                                //       (Number(aAmount) * tokenA.price) /
                                //       token.price;
                                //     setBAmount(
                                //       expectedB.toFixed(token.decimals)
                                //     );
                                //   } else {
                                //     setBAmount("");
                                //   }
                                // } else {
                                //   setBAmount("");
                                // }
                              }}
                              className="flex items-center gap-3 p-3 hover:bg-[#f1f5f9] cursor-pointer border-b border-black rounded-"
                            >
                              {token.logo ? (
                                <img
                                  src={token.logo}
                                  alt={token.symbol}
                                  className="w-6 h-6 rounded-full object-contain"
                                />
                              ) : (
                                <div className="w-6 h-6 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold">
                                  {token.symbol.slice(0, 3)}
                                </div>
                              )}
                              <span className="font-medium">
                                {token.symbol}
                              </span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    $
                    {calculateUSDValue(
                      bAmount,
                      selectedTokenB?.price * masPriceInUSD
                    )}
                  </div>
                  {insufficientBBalance && (
                    <p className="text-red-500 text-sm mt-1">
                      Insufficient balance
                    </p>
                  )}
                </div>
              </div>
              {estimatedLPAmount && (
                <div className="bg-[#FEF3C7] p-3 rounded-xl mt-4 text-sm text-[#92400E] text-center">
                  Estimated LP tokens:{" "}
                  {parseFloat(estimatedLPAmount).toFixed(4)} LP{" "}
                  {masPriceInUSD > 0 && (
                    <>
                      (≈ $
                      {(
                        ((parseFloat(estimatedLPAmount) *
                          (reserveA * tokenA.price + reserveB * tokenB.price)) /
                          Number(
                            formatUnits(
                              totalLpSupply,
                              Math.max(tokenA.decimals, tokenB.decimals)
                            )
                          )) *
                        masPriceInUSD
                      ).toFixed(2)}
                      )
                    </>
                  )}
                </div>
              )}
              <button
                className={`w-full border-b-[6px] border-2 border-[#000000] bg-[#F6C955] hover:opacity-90 text-[#333333] font-semibold py-3 rounded-xl transition mt-4 ${
                  isAddDisabled || isProcessing
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
                disabled={isAddDisabled || isProcessing}
                onClick={handleAddLiquidity}
              >
                {isProcessing ? "Processing..." : "Add Liquidity"}
              </button>
            </>
          )}
          {mode === "remove" && isConnected && (
            <>
              <div className="p-3  border-[#e3e6f1] rounded-lg border-2 bg-[#E6EBEC]  mb-6">
                <div className="flex justify-between mb-7">
                  <span className="text-gray-600">Amount to remove</span>
                  <span className="text-gray-600">
                    Balance:{" "}
                    <BalanceDisplay
                      value={Number(
                        formatUnits(
                          lpBalance,
                          Math.max(tokenA.decimals, tokenB.decimals)
                        )
                      )}
                      decimals={8}
                      suffix=" LP"
                    />
                  </span>
                </div>
                <div className="relative pt-1 pb-6">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={removePercentage}
                    onChange={(e) =>
                      setRemovePercentage(Number(e.target.value))
                    }
                    className="w-full range-slider"
                    style={{
                      background: `linear-gradient(to right, #F6C955 ${removePercentage}%, #fff ${removePercentage}%)`,
                    }}
                  />
                  <div
                    className="absolute text-center transform -translate-x-1/2 -translate-y-8"
                    style={{ left: `${removePercentage}%`, top: "8px" }}
                  >
                    <div className="bg-[#F6C955] text-white text-xs font-medium px-2 py-1 rounded-lg shadow-lg">
                      {removePercentage}%
                      <div className="absolute w-2 h-2 bg-[#F6C955] transform -translate-x-1/2 -bottom-1 left-1/2 rotate-45" />
                    </div>
                  </div>
                </div>
                <div className="flex justify-between gap-2">
                  {[25, 50, 75, 100].map((pct) => (
                    <button
                      key={pct}
                      className={`flex-1 py-2 text-sm rounded-lg border-2 border-[#1E1E1E] text-[#1E1E1E] ${
                        removePercentage === pct
                          ? "bg-[#F6C955] "
                          : "bg-white  hover:bg-gray-100"
                      }`}
                      onClick={() => setRemovePercentage(pct)}
                    >
                      {pct}%
                    </button>
                  ))}
                </div>
                <div className="mt-4 flex justify-between items-center py-3 bg-[#E6EBEC]">
                  <span className="text-sm text-gray-600">You receive:</span>
                  <div className="flex gap-4">
                    <span className="font-medium">
                      {(
                        (reserveA * poolShare * removePercentage) /
                        10000
                      ).toFixed(2)}{" "}
                      {tokenA?.symbol}
                    </span>
                    <span className="font-medium">
                      {(
                        (reserveB * poolShare * removePercentage) /
                        10000
                      ).toFixed(2)}{" "}
                      {tokenB?.symbol}
                    </span>
                  </div>
                </div>
              </div>

              <button
                className={`w-full border-b-[6px] border-2 border-[#000000] bg-[#F6C955] hover:opacity-90 text-[#333333] font-semibold py-3 rounded-xl transition  ${
                  isProcessing || lpBalance === 0n
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
                onClick={handleRemoveLiquidity}
                disabled={isProcessing || lpBalance === 0n}
              >
                {isProcessing
                  ? "Processing..."
                  : `Remove ${(
                      (Number(
                        formatUnits(
                          lpBalance,
                          Math.max(tokenA.decimals, tokenB.decimals)
                        )
                      ) *
                        removePercentage) /
                      100
                    ).toFixed(4)} LP`}
              </button>
            </>
          )}
        </div>

        {/* CSS for the animation */}
        <style>{`
         .number.increase.animating {
          animation: flashIncrease 0.5s ease-in-out;
        }
        @keyframes flashIncrease {
          0%, 100% { color: inherit; }
          50% { color: #22c55e; }
        }
        .number.decrease.animating {
          animation: flashDecrease 0.5s ease-in-out;
        }
        @keyframes flashDecrease {
          0%, 100% { color: inherit; }
          50% { color: #ef4444; }
        }
       
          .range-slider {
            -webkit-appearance: none;
            height: 6px;
            border-radius: 3px;
            outline: none;
          }
          .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: #FFFFFF;
            border: 3px solid #F6C955;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
          }
          .range-slider::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
          }
          .range-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: #FFFFFF;
            border: 3px solid #F6C955;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
          }
          .range-slider::-moz-range-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
          }
        `}</style>
      </div>
    </div>
  );
};

export default LiquidityModal;
