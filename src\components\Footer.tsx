import { <PERSON>a<PERSON><PERSON><PERSON>, FaGith<PERSON>, FaTelegramPlane } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { LuExternalLink } from "react-icons/lu";

const Footer = () => {
  return (
    <footer className="text-black py-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Logo and Socials */}
          <div className="flex flex-col items-center lg:items-start justify-center item">
            <div className="flex items-center mb-4 w-full justify-center">
              <img
                src="/images/footer logo.svg"
                alt="EagleFi"
                className="w-24"
              />
              {/* <span className="text-xl font-semibold">EagleFi</span> */}
            </div>
            <div className="flex gap-4 mt-2">
              <a
                href="https://github.com/NaDasai/eagle-finance"
                target="_blank"
                className="p-2 rounded-full transition-colors"
              >
                <FaGithub className="text-black" size="20" />
              </a>
              <a
                href="https://discord.gg/E4XUPEeudB"
                target="_blank"
                className=" p-2 rounded-full transition-colors"
              >
                <FaDiscord className="text-black" size="20" />
              </a>
              <a
                href="https://x.com/Eaglefi_Massa"
                target="_blank"
                className="0 p-2 rounded-full transition-colors"
              >
                <FaXTwitter className="text-black" size="20" />
              </a>
              <a
                href="https://t.me/eaglefi_community"
                target="_blank"
                className="0 p-2 rounded-full transition-colors"
              >
                <FaTelegramPlane className="text-black" size="20" />
              </a>
            </div>
          </div>

          {/* ECOSYSTEM */}
          <div>
            <h3 className="font-extrabold mb-4">ECOSYSTEM</h3>
            <ul className="space-y-2">
              <li>
                <a href="/" className="hover:text-gray-800 font-medium">
                  Trade
                </a>
              </li>
              <li>
                <a
                  href="/liquidity"
                  className="hover:text-gray-800 font-medium"
                >
                  Earn
                </a>
              </li>
              <li>
                <a
                  href="/flappy-eagle"
                  className="hover:text-gray-800 font-medium"
                >
                  Play
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-gray-800 font-medium flex items-center gap-1"
                >
                  Merchandise
                  <span className=" border border-black text-[8px] px-1 py-0 rounded-full">
                    SOON
                  </span>
                </a>
              </li>
            </ul>
          </div>

          {/* BUSINESS */}
          <div>
            <h3 className="font-extrabold mb-4">BUSINESS</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="/create-token"
                  className="hover:text-gray-800 font-medium"
                >
                  Create Token
                </a>
              </li>
              <li>
                <a
                  href="/swap-widget-creator"
                  className="hover:text-gray-800 font-medium"
                >
                  Create Swap Widget
                </a>
              </li>
            </ul>
          </div>

          {/* SUPPORT */}
          <div>
            <h3 className="font-extrabold mb-4">SUPPORT</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="https://discord.gg/E4XUPEeudB"
                  target="_blank"
                  className="hover:text-gray-800 font-medium"
                >
                  <div className="flex items-center gap-1">
                    Get Help <LuExternalLink />
                  </div>
                </a>
              </li>
              <li>
                <a
                  href="https://discord.gg/E4XUPEeudB"
                  target="_blank"
                  className="hover:text-gray-800 font-medium"
                >
                  <div className="flex items-center gap-1">
                    Troubleshooting <LuExternalLink />
                  </div>
                </a>
              </li>
              <li>
                <a
                  href="https://docs.eaglefi.io"
                  className="hover:text-gray-800 font-medium"
                  target="_blank"
                >
                  <div className="flex items-center gap-1">
                    Documentation <LuExternalLink />
                  </div>
                </a>
              </li>
              <li>
                <a
                  href="https://sayfer.io/audits/smart-contract-audit-eagle-finance/"
                  className="hover:text-gray-800 font-medium flex items-center gap-1"
                  target="_blank"
                >
                  <div className="flex items-center gap-1">
                    Audits <LuExternalLink />
                  </div>
                </a>
              </li>
            </ul>
          </div>

          {/* ABOUT */}
          <div>
            <h3 className="font-extrabold mb-4">ABOUT</h3>
            <ul className="space-y-2">
              <li>
                <a href="/blog" className="hover:text-gray-800 font-medium">
                  Blog
                </a>
              </li>
              <li>
                <a
                  href="/terms-of-service"
                  className="hover:text-gray-800 font-medium"
                >
                  Terms Of Service
                </a>
              </li>
            </ul>
          </div>
          {/* Developers */}
          <div>
            <h3 className="font-extrabold mb-4">DEVELOPERS</h3>
            <ul className="space-y-2 ">
              <li>
                <a
                  href="https://github.com/NaDasai/eagle-finance"
                  target="_blank"
                  className="hover:text-gray-800 font-medium"
                >
                  <div className="flex items-center gap-1">
                    Github <LuExternalLink />
                  </div>
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* DEVELOPERS section moved to bottom for mobile responsiveness */}
        <div className="mt-8 text-center lg:hidden">
          <h3 className="font-bold mb-4">DEVELOPERS</h3>
          <ul className="space-y-2">
            <li>
              <a
                href="https://github.com/NaDasai/eagle-finance"
                className="hover:text-gray-800 font-medium"
              >
                Github
              </a>
            </li>
          </ul>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
