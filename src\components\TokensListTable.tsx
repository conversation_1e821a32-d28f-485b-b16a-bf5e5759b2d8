import { useNavigate } from "react-router-dom";
import { NATIVE_MAS_COIN_ADDRESS } from "../lib/utils";
import { useEffect, useRef, useState } from "react";
import { FaDollarSign } from "react-icons/fa";
import axiosInstance from "../lib/axios/axiosInstance";
// import CountUp from "react-countup";
import { TbArrowsSort } from "react-icons/tb";
import TokenLogo from "./TokenLogo";
import { prettyPrice } from "../lib/utils2";

type StatCardProps = {
  value: number;
  label: string;
  change?: string;
  isPositive?: boolean;
  icon?: JSX.Element;
  currency: "MAS" | "USD";
};
// const usePrevious = (value: number) => {
//   const ref = useRef<number>();
//   useEffect(() => {
//     ref.current = value;
//   });
//   return ref.current;
// };

const StatCard = ({
  value,
  label,
  change,
  isPositive,
  icon,
  currency,
}: StatCardProps) => {
  // const [isInView, setIsInView] = useState(false);
  // const hasAnimatedRef = useRef(false);
  const cardRef = useRef<HTMLDivElement>(null);
  // const previousValue = usePrevious(value); // Get previous value

  // useEffect(() => {
  //   const observer = new IntersectionObserver(
  //     ([entry]) => {
  //       if (entry.isIntersecting) {
  //         setIsInView(true);
  //         observer.unobserve(entry.target);
  //       }
  //     },
  //     { threshold: 0.1 }
  //   );

  //   if (cardRef.current) observer.observe(cardRef.current);
  //   return () => {
  //     if (cardRef.current) observer.unobserve(cardRef.current);
  //   };
  // }, []);

  return (
    // <div
    //   ref={cardRef}
    //   className="p-4 rounded-md bg-[#F9FAFB] border-2 border-b-4 border-[#1E1E1E] shadow-sm flex flex-col gap-3 min-w-[160px] md:min-w-[200px] transition-all hover:shadow-md"
    // >
    //   <p className="text-gray-900 text-lg md:text-xl font-semibold leading-tight">
    //     {isInView ? (
    //       <CountUp
    //         start={previousValue !== undefined ? previousValue : 0} // Use previous value as start
    //         end={value}
    //         duration={hasAnimatedRef.current ? 0 : 2}
    //         onEnd={() => (hasAnimatedRef.current = true)}
    //         separator=","
    //         formattingFn={(val) =>
    //           `${currency === "USD" ? "$" : ""}${formatLargeNumber(val)}${
    //             currency === "MAS" ? " MAS" : ""
    //           }`
    //         }
    //       />
    //     ) : (
    //       `${currency === "USD" ? "$0" : "0 MAS"}`
    //     )}
    //   </p>
    //   <div className="flex flex-col md:flex-row md:items-center justify-between gap-2 text-sm">
    //     <p className="text-gray-600 font-medium">{label}</p>
    //     <span
    //       className={`flex items-center gap-1 font-medium ${
    //         isPositive ? "text-green-600" : "text-red-600"
    //       }`}
    //     >
    //       {icon}
    //       {change}
    //     </span>
    //   </div>
    // </div>
    <div
      ref={cardRef}
      className="p-4 rounded-md bg-[#F9FAFB] border-2 border-b-4 border-[#1E1E1E] shadow-sm flex flex-col gap-3 min-w-[160px] md:min-w-[200px] transition-all hover:shadow-md"
    >
      <p className="text-gray-900 text-lg md:text-xl font-semibold leading-tight">
        {`${currency === "USD" ? "$" : ""}${formatLargeNumber(value)}${
          currency === "MAS" ? " MAS" : ""
        }`}
      </p>
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-2 text-sm">
        <p className="text-gray-600 font-medium">{label}</p>
        <span
          className={`flex items-center gap-1 font-medium ${
            isPositive ? "text-green-600" : "text-red-600"
          }`}
        >
          {icon}
          {change}
        </span>
      </div>
    </div>
  );
};

export const UpArrowIcon = (
  <svg
    className="w-4 h-4"
    style={{
      height: "1em",
      verticalAlign: "-0.125em",
      transformOrigin: "center",
      overflow: "visible",
    }}
    viewBox="0 0 320 512"
    aria-hidden="true"
    role="img"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g transform="translate(160 256)">
      <g transform="translate(0,0) scale(1,1)">
        <path
          d="M182.6 137.4c-12.5-12.5-32.8-12.5-45.3 0l-128 128c-9.2 9.2-11.9 22.9-6.9 34.9s16.6 19.8 29.6 19.8l256 0c12.9 0 24.6-7.8 29.6-19.8s2.2-25.7-6.9-34.9l-128-128z"
          fill="currentColor"
          transform="translate(-160 -256)"
        ></path>
      </g>
    </g>
  </svg>
);

export const DownArrowIcon = (
  <svg
    className="w-4 h-4"
    style={{
      height: "1em",
      verticalAlign: "-0.125em",
      transformOrigin: "center",
      overflow: "visible",
    }}
    viewBox="0 0 320 512"
    aria-hidden="true"
    role="img"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g transform="translate(160 256)">
      <g transform="translate(0,0) scale(1,1)">
        <path
          d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"
          fill="currentColor"
          transform="translate(-160 -256)"
        ></path>
      </g>
    </g>
  </svg>
);

interface Token {
  status: string;
  address: string;
  symbol: string;
  name: string;
  logo: string;
  price_mas: number;
  price_usd: number;
  price_24h_percentage_change: number;
  price_24h_mas_percentage_change: number;
  market_cap_mas: number;
  market_cap_usd: number;
  volume_24h_mas: number;
  volume_24h_usd: number;
  total_liquidity_mas: number;
  total_liquidity_usd: number;
  rank: number;
}

export const formatLargeNumber = (value: number): string => {
  if (value == null || isNaN(value)) {
    return "0";
  }
  if (value >= 1e12) {
    return (value / 1e12).toFixed(1) + "T";
  } else if (value >= 1e9) {
    return (value / 1e9).toFixed(1) + "B";
  } else if (value >= 1e6) {
    return (value / 1e6).toFixed(1) + "M";
  } else if (value >= 1e3) {
    return (value / 1e3).toFixed(1) + "K";
  } else {
    return value.toLocaleString(undefined, { maximumFractionDigits: 2 });
  }
};

const TokensListTable = () => {
  const navigate = useNavigate();
  const [currency, setCurrency] = useState<"MAS" | "USD">("USD");
  const [tokens, setTokens] = useState<Token[]>([]);
  const [totalVolume24h, setTotalVolume24h] = useState({
    mas: 0,
    usd: 0,
    percentage_change: 0,
  });
  const [totalTVL, setTotalTVL] = useState({
    mas: 0,
    usd: 0,
    percentage_change: 0,
  });
  const [totalVolume, setTotalVolume] = useState({ mas: 0, usd: 0 });
  const [isLoading, setIsLoading] = useState(true);

  // Sorting state

  const [sortConfig, setSortConfig] = useState<{
    field: keyof Token | null;
    isDesc: boolean;
  }>({ field: null, isDesc: false });

  const getSortField = (
    header: "24h%" | "marketCap" | "24hVolume"
  ): keyof Token => {
    switch (header) {
      case "24h%":
        return "price_24h_percentage_change";
      case "marketCap":
        return currency === "USD" ? "market_cap_usd" : "market_cap_mas";
      case "24hVolume":
        return currency === "USD" ? "volume_24h_usd" : "volume_24h_mas";
      default:
        return "rank"; // Fallback, though not used
    }
  };

  const handleSort = (header: "24h%" | "marketCap" | "24hVolume") => {
    const sortField = getSortField(header);
    setSortConfig((prev) => {
      if (prev.field !== sortField) {
        return { field: sortField, isDesc: false };
      } else if (prev.isDesc) {
        return { field: null, isDesc: false }; // Reset sorting
      } else {
        return { field: sortField, isDesc: true };
      }
    });
  };

  const sortTokens = (tokensToSort: Token[]): Token[] => {
    if (!sortConfig.field) return tokensToSort;

    return [...tokensToSort].sort((a, b) => {
      const aValue = a[sortConfig.field!];
      const bValue = b[sortConfig.field!];
      if (sortConfig.isDesc) {
        return bValue > aValue ? 1 : -1;
      }
      return aValue > bValue ? 1 : -1;
    });
  };

  const renderSortIcon = (header: "24h%" | "marketCap" | "24hVolume") => {
    const sortField = getSortField(header);
    if (sortConfig.field !== sortField) {
      return <TbArrowsSort className="w-4 h-4 text-gray-400" />;
    }
    return sortConfig.isDesc ? DownArrowIcon : UpArrowIcon;
  };

  useEffect(() => {
    setSortConfig({ field: null, isDesc: false }); // Reset sorting when currency changes
  }, [currency]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [tokensResponse, globalStatsResponse] = await Promise.all([
          axiosInstance.get<Token[]>("/tokens/stats"),
          axiosInstance.get<{
            total_volume_24h: {
              mas: number;
              usd: number;
              percentage_change: number;
            };
            total_tvl: { mas: number; usd: number; percentage_change: number };
            total_volume: { mas: number; usd: number };
          }>("/statistics/global"),
        ]);

        const sortedTokens = sortTokens(tokensResponse.data);
        setTokens(sortedTokens);
        setTotalVolume24h(globalStatsResponse.data.total_volume_24h);
        setTotalTVL(globalStatsResponse.data.total_tvl);
        setTotalVolume(globalStatsResponse.data.total_volume);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData(); // Initial fetch
    const intervalId = setInterval(fetchData, 5000); // Poll every 5 seconds
    return () => clearInterval(intervalId);
  }, [sortConfig.field, sortConfig.isDesc]); // Re-run when sort config changes to apply sorting

  // const formatPrice = (price: number) => {
  //   if (price == null) return "N/A";
  //   return price.toLocaleString(undefined, { maximumFractionDigits: 4 });
  // };
  return (
    <div className="container mx-auto px-4 md:px-6 lg:px-8 mb-8">
      <div className="relative  bg-white border-2 border-[#1E1E1E] rounded-3xl p-6">
        <img
          src="/images/nest.png"
          alt="Nest"
          className="absolute left-6 -top-9 max-sm:-top-5 w-32 max-sm:w-20 h-auto z-10"
        />
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6 flex-wrap">
          {/* Left: Title and Subtitle */}
          <div className="order-1 md:order-none">
            <h2 className="text-3xl font-bold text-slate-950 mb-1">
              EagleMarketCap
            </h2>
            <span className="text-sm text-stone-800">
              Real-time prices and market data.
            </span>
          </div>

          {/* Middle: Stat Cards */}
          <div className="order-2 md:order-none">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
              <StatCard
                value={currency === "MAS" ? totalVolume.mas : totalVolume.usd}
                currency={currency}
                label="Total Volume"
                // change={totalVolume24h.percentage_change.toFixed(1) + "%"}
                // isPositive={totalVolume24h.percentage_change >= 0}
                // icon={
                //   totalVolume24h.percentage_change >= 0
                //     ? UpArrowIcon
                //     : DownArrowIcon
                // }
              />
              <StatCard
                value={
                  currency === "MAS" ? totalVolume24h.mas : totalVolume24h.usd
                }
                currency={currency}
                label="Volume 24h"
                change={totalVolume24h.percentage_change.toFixed(1) + "%"}
                isPositive={totalVolume24h.percentage_change >= 0}
                icon={
                  totalVolume24h.percentage_change >= 0
                    ? UpArrowIcon
                    : DownArrowIcon
                }
              />

              <StatCard
                value={currency === "MAS" ? totalTVL.mas : totalTVL.usd}
                currency={currency}
                label="Total Value Locked"
                change={totalTVL.percentage_change.toFixed(1) + "%"}
                isPositive={totalTVL.percentage_change >= 0}
                icon={
                  totalTVL.percentage_change >= 0 ? UpArrowIcon : DownArrowIcon
                }
              />
            </div>
          </div>

          {/* Right: Search and Currency Toggle */}
          <div className="order-3 md:order-none w-full md:w-auto">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              {/* <div className="flex items-center rounded-3xl px-3 py-3 border border-[#e3e6f1] max-w-md">
              <svg
                aria-hidden="true"
                focusable="false"
                data-prefix="fas"
                data-icon="magnifying-glass"
                className="w-4 h-4 text-gray-500 mr-2"
                role="img"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 512 512"
              >
                <path
                  fill="currentColor"
                  d="M416 208c0 45.9-14.9 88.3-40 
                     122.7L502.6 457.4c12.5 12.5 
                     12.5 32.8 0 45.3s-32.8 12.5-45.3 
                     0L330.7 376c-34.4 25.2-76.8 
                     40-122.7 40C93.1 416 0 
                     322.9 0 208S93.1 0 208 
                     0s208 93.1 208 208zM208 
                     352a144 144 0 1 0 0-288 
                     144 144 0 1 0 0 288z"
                />
              </svg>
              <input
                type="text"
                placeholder="Search tokens..."
                className="bg-transparent w-full text-sm text-slate-700 placeholder:text-gray-500 focus:outline-none"
              />
            </div> */}
              <div className="w-full md:w-auto">
                <div className="flex h-11 flex-row items-center gap-2 rounded-lg border-2 border-[#1E1E1E] bg-[#F7FAFC] p-1 w-full sm:w-max">
                  <button
                    onClick={() => setCurrency("MAS")}
                    className={`flex h-8 w-full cursor-pointer flex-row items-center justify-center gap-2 px-3 py-2 font-medium text-base sm:text-xl ${
                      currency === "MAS"
                        ? "bg-[#0000001b]  rounded-lg border border-[#000000] text-gray-800"
                        : "text-[#718096]  "
                    }`}
                  >
                    <div className="flex h-4 w-4 items-center justify-center rounded-full bg-white">
                      <img
                        src="/images/massa.jpg"
                        alt="MAS"
                        className="rounded-full"
                      />
                    </div>
                    MAS
                  </button>
                  <button
                    onClick={() => setCurrency("USD")}
                    className={`flex h-8 w-full cursor-pointer flex-row items-center justify-center gap-2 px-3 py-2 font-medium text-base sm:text-xl ${
                      currency === "USD"
                        ? "bg-[#0000001b] rounded-lg border border-[#000000] text-gray-800"
                        : "text-[#718096] "
                    }`}
                  >
                    <div
                      className={`flex h-4 w-4 items-center justify-center rounded-full ${
                        currency === "USD" ? "bg-[#2D3748]" : "bg-[#718096]"
                      }`}
                    >
                      <span
                        className={`${
                          currency === "USD" ? "text-white" : "text-white"
                        } text-xs font-semibold`}
                      >
                        {/* $ */}
                        <FaDollarSign />
                      </span>
                    </div>
                    USD
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Table */}
        <div className="overflow-x-auto rounded-xl border-2 border-[#000000] border-b-[6px] border-r-[6px] hidden md:block">
          <table className="w-full table-auto text-sm ">
            <thead className="bg-[#E6EBEC]">
              <tr>
                <th className="py-3 px-4 text-left text-[#1E1E1E] font-bold">
                  #
                </th>

                <th className="py-3 px-4 text-left text-[#1E1E1E] font-bold">
                  Token
                </th>
                <th className="py-3 px-4 text-left text-[#1E1E1E] font-bold">
                  Price
                </th>
                <th className="py-3 px-4 text-left text-[#1E1E1E] font-bold">
                  <button
                    onClick={() => handleSort("24h%")}
                    className="flex items-center gap-1  p-1 "
                  >
                    24h %{renderSortIcon("24h%")}
                  </button>
                </th>
                <th className="py-3 px-4 text-left text-[#1E1E1E] font-bold">
                  <button
                    onClick={() => handleSort("marketCap")}
                    className="flex items-center gap-1  p-1"
                  >
                    Market Cap
                    {renderSortIcon("marketCap")}
                  </button>
                </th>
                <th className="py-3 px-4 text-left text-[#1E1E1E] font-bold">
                  <button
                    onClick={() => handleSort("24hVolume")}
                    className="flex items-center gap-1  p-1"
                  >
                    24h Volume
                    {renderSortIcon("24hVolume")}
                  </button>
                </th>
              </tr>
            </thead>
            <tbody className="bg-[#F6F6F6] ">
              {isLoading
                ? Array.from({ length: 6 }).map((_, index) => (
                    <tr
                      key={index}
                      className={`animate-pulse ${
                        index % 2 === 0 ? "bg-[#F0F4F5]" : "bg-white"
                      }`}
                    >
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <div className="w-7 h-7 rounded-full bg-gray-200"></div>
                          <div className="space-y-1">
                            <div className="h-4 bg-gray-200 rounded w-24"></div>
                            <div className="h-3 bg-gray-200 rounded w-16"></div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="h-4 bg-gray-200 rounded w-16"></div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                      </td>
                    </tr>
                  ))
                : tokens.map((token, index) => (
                    <tr
                      key={token.address}
                      className={`hover:bg-[#E6EBEC] border-b border-b-gray-200 ease-in-out duration-200 cursor-pointer ${
                        index % 2 !== 0 ? "bg-[#F0F4F5]" : "bg-white"
                      }`}
                      onClick={() =>
                        navigate(
                          `/token/${
                            token.address === "NATIVE_COIN"
                              ? NATIVE_MAS_COIN_ADDRESS
                              : token.address
                          }`
                        )
                      }
                    >
                      <td className="py-3 pr-0 " style={{ width: "35px" }}>
                        <div>{token.rank}</div>
                      </td>
                      <td className="py-3 px-4 ">
                        <div className="flex items-center gap-2">
                          {token.logo ? (
                            <div className="relative">
                              <TokenLogo token={token} showBadge={true} />
                              {/* <img
                                src={token.logo}
                                alt={token.symbol}
                                className="w-7 h-auto rounded-full border border-white"
                              /> */}
                              {/* {token.status === "OFFICIAL" && (
                                <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border border-black ">
                                  <img
                                    src="/images/massa.jpg"
                                    alt="Official"
                                    className="rounded-full"
                                  />
                                </div>
                              )}
                              {token.status === "PARTNER" && (
                                <div className="absolute -bottom-1 -right-0 w-4 h-4  ">
                                  🤝
                                </div>
                              )} */}
                            </div>
                          ) : (
                            <div className="relative">
                              <div className="w-7 h-7 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold border border-white">
                                {token.symbol.slice(0, 3)}
                              </div>
                              {/* {token.status === "OFFICIAL" && (
                                <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border border-black ">
                                  <img
                                    src="/images/massa.jpg"
                                    alt="Official"
                                    className="rounded-full"
                                  />
                                </div>
                              )}
                              {token.status === "PARTNER" && (
                                <div className="absolute -bottom-1 -right-1 w-4 h-4 ">
                                  🤝
                                </div>
                              )} */}
                            </div>
                          )}
                          <div>
                            <div className="font-bold text-lg text-[#1E1E1E] ">
                              {token.name}
                            </div>
                            <div className="font-bold text-xs text-gray-500">
                              {token.symbol}{" "}
                              {token.status === "OFFICIAL" ? "(OFFICIAL) " : ""}
                              {token.status === "PARTNER" ? "(PARTNER) " : ""}
                              {token.status === "CAUTION" ? "(CAUTION) " : ""}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td
                        className="py-3 px-4 text-[#1E1E1E] font-bold"
                        key={`${token.address}-${currency}`}
                      >
                        {token?.price_usd &&
                          prettyPrice(
                            currency === "MAS"
                              ? token.price_mas
                              : token.price_usd,
                            currency
                          )}
                      </td>
                      <td
                        className={`py-3 px-4 font-bold ${
                          (currency === "MAS"
                            ? token.price_24h_mas_percentage_change
                            : token.price_24h_percentage_change) >= 0
                            ? "text-green-500"
                            : "text-red-500"
                        }`}
                      >
                        {(currency === "MAS"
                          ? token.price_24h_mas_percentage_change
                          : token.price_24h_percentage_change
                        ).toFixed(2)}
                        %
                      </td>

                      <td className="py-3 px-4 font-bold text-[#1E1E1E] ">
                        {currency === "MAS" ? "" : "$"}
                        {formatLargeNumber(
                          currency === "MAS"
                            ? token.market_cap_mas
                            : token.market_cap_usd
                        )}
                        {currency === "MAS" ? " MAS" : ""}
                      </td>
                      <td className="py-3 px-4 text-[#1E1E1E] font-bold ">
                        {currency === "MAS" ? "" : "$"}
                        {formatLargeNumber(
                          currency === "MAS"
                            ? token.volume_24h_mas
                            : token.volume_24h_usd
                        )}
                        {currency === "MAS" ? " MAS" : ""}
                      </td>
                    </tr>
                  ))}
            </tbody>
          </table>
        </div>

        {/* Mobile Cards for Tokens */}
        <div className="md:hidden space-y-4 mt-4">
          {tokens.map((token) => (
            <div
              key={token.address}
              className="rounded-md bg-[#F9FAFB] border-2 border-b-4 border-[#1E1E1E]  p-4  transition-colors"
              onClick={() =>
                navigate(
                  `/token/${
                    token.address === "NATIVE_COIN"
                      ? NATIVE_MAS_COIN_ADDRESS
                      : token.address
                  }`
                )
              }
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                  {token.logo ? (
                    <div className="relative min-w-6">
                      <TokenLogo token={token} showBadge={true} />
                    </div>
                  ) : (
                    // <img
                    //   src={token.logo}
                    //   alt={token.symbol}
                    //   className="w-8 h-8 rounded-full border border-white"
                    // />
                    <div className="w-8 h-8 flex items-center justify-center rounded-full bg-slate-300 text-white font-semibold">
                      {token.symbol}
                    </div>
                  )}
                  <div>
                    <div className="font-medium text-[#1E1E1E]">
                      {token.name}
                    </div>
                    <div className="text-sm text-gray-500">{token.symbol}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-[#1E1E1E]">
                    {prettyPrice(
                      currency === "MAS" ? token.price_mas : token.price_usd,
                      currency
                    )}
                  </div>
                  <div
                    className={`text-sm ${
                      (currency === "MAS"
                        ? token.price_24h_mas_percentage_change
                        : token.price_24h_percentage_change) >= 0
                        ? "text-green-500"
                        : "text-red-500"
                    }`}
                  >
                    {(currency === "MAS"
                      ? token.price_24h_mas_percentage_change
                      : token.price_24h_percentage_change
                    ).toFixed(1)}
                    %
                  </div>

                  <div className="text-sm text-gray-500">
                    {currency === "MAS" ? "" : "$"}
                    {formatLargeNumber(
                      currency === "MAS"
                        ? token.market_cap_mas
                        : token.market_cap_usd
                    )}
                    {currency === "MAS" ? " MAS" : ""}
                  </div>
                  <div className="text-sm text-gray-500">
                    Vol: {currency === "MAS" ? "" : "$"}
                    {formatLargeNumber(
                      currency === "MAS"
                        ? token.volume_24h_mas
                        : token.volume_24h_usd
                    )}
                    {currency === "MAS" ? " MAS" : ""}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TokensListTable;
