/* -------------------------------------------------------------------------- */
/* 1 ▸ TokenLogo                                                               */
/* -------------------------------------------------------------------------- */
import React, { useEffect, useState } from "react";

interface TokenLogoProps {
  token: any;
  width?: string;
  height?: string;
  className?: string;
  showBadge?: boolean;
}

const TokenLogo: React.FC<TokenLogoProps> = ({
  token,
  showBadge = false,
  ...props
}) => {
  const [errored, setErrored] = useState(false);
  useEffect(() => {
    setErrored(false);
  }, [token.logo]);

  const renderBadge = (token: any) => {
    if (!showBadge) return null;
    if (token.status === "OFFICIAL") {
      return (
        <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border border-black">
          <img
            src="/images/massa.jpg"
            alt="Official"
            className="rounded-full"
          />
        </div>
      );
    }

    if (token.status === "PARTNER") {
      return <div className="absolute -bottom-1 -right-0 w-4 h-4">🤝</div>;
    }
    if (token.status === "CAUTION") {
      return <div className="absolute -bottom-1 right-1 w-2 h-2">⚠️</div>;
    }

    return null;
  };

  const fallback = (
    <div
      className={`${props.width || "w-6"} ${props.height || "h-6"} ${
        props.className || ""
      } flex items-center justify-center rounded-full bg-slate-300 text-white text-[10px] font-semibold border-2 border-white`}
    >
      {token.symbol.slice(0, 4).toUpperCase()}
    </div>
  );

  if (errored || !token.logo) return fallback;

  return (
    <>
      <img
        src={token.logo}
        alt={token.symbol}
        className={`${props.width || "w-6"} ${
          props.height || "h-6"
        } rounded-full  ${props.className || ""}`}
        onError={() => setErrored(true)}
      />
      {renderBadge(token)}
    </>
  );
};

export default TokenLogo;
