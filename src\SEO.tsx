// src/SEO.tsx
import { Helmet } from "react-helmet-async";
import { useLocation, matchPath } from "react-router-dom";

const routeMeta = [
  {
    path: "/",
    title: "EagleFi - Decentralized Exchange on Massa",
    desc: "Swap tokens, provide liquidity, and earn fees on Massa blockchain.",
  },
  {
    path: "/create-token",
    title: "Create Token – EagleFi",
    desc: "Launch your own token on Massa in minutes with EagleFi’s Create Token wizard.",
  },
  {
    path: "/create-pool",
    title: "Create Pool – EagleFi",
    desc: "Provide liquidity and start earning fees: create a pool on Massa blockchain.",
  },
  {
    path: "/portfolio",
    title: "Portfolio – EagleFi",
    desc: "Track your Assets holdings & pool shares in your EagleFi portfolio.",
  },
  {
    path: "/liquidity",
    title: "Liquidity Pools – EagleFi",
    desc: "Browse all active liquidity pools on EagleFi.",
  },
  { path: "/bridge", title: "Bridge – EagleFi", desc: "Bridge assets." },
  // {
  //   path: "/bridge",
  //   title: "Bridge – EagleFi",
  //   desc: "Bridge assets between different blockchains using Massa bridge.",
  // },
  {
    path: "/send",
    title: "Send Crypto – EagleFi",
    desc: "Send crypto to any wallet address on Massa blockchain.",
  },
  {
    path: "/eagle-assistant",
    title: "Eagle Assistant – EagleFi",
    desc: "Get instant answers to your questions with EagleFi's AI assistant.",
  },
  {
    path: "/terms-of-service",
    title: "Terms of Service – EagleFi",
    desc: "Read EagleFi's terms of service.",
  },
  {
    path: "/flappy-eagle",
    title: "Flappy Eagle – EagleFi",
    desc: "Play Flappy Eagle, the addictive game by EagleFi.",
  },
  {
    path: "/blog",
    title: "Blog – EagleFi",
    desc: "Read guides, announcements, and deep dives on Massa & DeFi.",
  },
];

export function SEO() {
  const { pathname } = useLocation();
  // find the matching routeMeta; default to homepage
  const meta =
    routeMeta.find((r) => matchPath(r.path, pathname)) ?? routeMeta[0];

  return (
    <Helmet>
      <title>{meta.title}</title>
      <meta name="description" content={meta.desc} />
      <link rel="canonical" href={`https://www.eaglefi.io${pathname}`} />
    </Helmet>
  );
}
