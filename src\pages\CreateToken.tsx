import React, { useEffect, useState } from "react";
import { useAccountStore } from "@massalabs/react-ui-kit";
import { deployToken } from "../services/deployToken";

import { toast } from "react-toastify";
import { FaGlobe } from "react-icons/fa";
import RedirectModal from "../components/RedirectModal";

//import { useDispatch } from "react-redux";
//import { addToken } from "../redux/features/tokensSlice";

const CreateTokenForm = () => {
  const [form, setForm] = useState({
    name: "",
    symbol: "",
    supply: "",
    logo: "",
    description: "",
    isPausable: false,
    isMintable: false,
    isBurnable: false,
    website: "",
  });
  const [status, setStatus] = useState("");
  const [isDeploying, setIsDeploying] = useState(false);

  const [operationId, setOperationId] = useState<string | null>(null);

  const { connectedAccount, currentWallet } = useAccountStore();
  const [network, setNetwork] = useState<string | null>(null);
  const isWalletConnected = !!connectedAccount;

  const [showRedirectModal, setShowRedirectModal] = useState(false);
  const [newTokenAddress, setNewTokenAddress] = useState("");
  const [tokenName, setTokenName] = useState("");

  useEffect(() => {
    const fetchNetworkInfo = async () => {
      if (connectedAccount && currentWallet) {
        const networkInfo = await currentWallet.networkInfos();
        const networkName = networkInfo?.name ?? null;
        setNetwork(networkName);
      } else {
        setNetwork(null);
      }
    };

    fetchNetworkInfo();

    const handleNetworkChange = async () => {
      await fetchNetworkInfo();
    };

    if (currentWallet) {
      currentWallet.listenNetworkChanges(handleNetworkChange);
    }
  }, [connectedAccount, currentWallet]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const errors = validateForm();
    if (errors.length > 0) {
      setStatus(errors.join(", "));
      toast.error(`Form errors: ${errors.join(", ")}`);
      return;
    }

    if (!form.name || !form.symbol || !form.supply) {
      setStatus("Please fill in all required fields (Name, Symbol, Supply).");
      toast.warn("Please fill in all required fields!");
      return;
    }

    if (!isWalletConnected) {
      setStatus("Wallet not connected. Please connect your wallet.");
      toast.warn("Wallet not connected. Please connect your wallet.");
      return;
    }

    setIsDeploying(true);
    setStatus("Deploying...");
    const deployingToastId = toast.loading("Deploying token...");
    setOperationId(null);

    try {
      const token = await deployToken(connectedAccount, form);
      console.log("Token deployed", token);
      setStatus(`Token ${token.name} deployed `);
      setOperationId(token.operationId);
      toast.update(deployingToastId, {
        render: (
          <div>
            {`Token "${token.name}" successfully deployed!`}
            <a
              href={
                network?.toUpperCase() === "BUILDNET"
                  ? `https://www.massexplo.com/tx/${token.operationId}?network=buildnet`
                  : `https://explorer.massa.net/mainnet/operation/${token.operationId}`
              }
              target="_blank"
              rel="noopener noreferrer"
              className="block mt-2 text-blue-600 hover:underline"
            >
              View on Explorer
            </a>
          </div>
        ),
        type: "success",
        isLoading: false,
        autoClose: 5000,
      });
      setNewTokenAddress(token?.address);
      setTokenName(token?.name);
      setShowRedirectModal(true);
      setForm({
        name: "",
        symbol: "",
        supply: "",
        logo: "",
        description: "",
        isPausable: false,
        isMintable: false,
        isBurnable: false,
        website: "",
      });
    } catch (error: any) {
      setStatus("Failed to deploy token");
      const errorMessage = operationId ? (
        <div>
          {`Token deployment failed: ${error?.message || "Unknown error"}`}
          <a
            href={
              network?.toUpperCase() === "BUILDNET"
                ? `https://www.massexplo.com/tx/${operationId}?network=buildnet`
                : `https://explorer.massa.net/mainnet/operation/${operationId}`
            }
            target="_blank"
            rel="noopener noreferrer"
            className="block mt-2 text-blue-600 hover:underline"
          >
            View on Explorer
          </a>
        </div>
      ) : (
        `Token deployment failed: ${error?.message || "Unknown error"}`
      );

      toast.update(deployingToastId, {
        render: errorMessage,
        type: "error",
        isLoading: false,
        autoClose: 5000,
      });
    } finally {
      setIsDeploying(false);
    }
  };

  const getButtonText = () => {
    if (!isWalletConnected) {
      return "Connect Wallet";
    }
    if (isDeploying) {
      return "Creating Token...";
    }
    return "Create Token";
  };

  const isButtonDisabled = !isWalletConnected || isDeploying;

  const validateForm = () => {
    const errors: string[] = [];

    if (!form.name.trim()) errors.push("Name is required");
    if (!form.symbol.trim()) errors.push("Symbol is required");
    if (!form.supply.trim()) errors.push("Supply is required");

    if (form.symbol.length > 5) errors.push("Symbol must be 2-5 characters");
    if (!/^[A-Z]+$/.test(form.symbol))
      errors.push("Symbol must be uppercase letters");

    if (isNaN(Number(form.supply)) || Number(form.supply) <= 0)
      errors.push("Supply must be a positive number");

    if (form.logo && !isValidUrl(form.logo)) errors.push("Invalid logo URL");

    if (form.website && !isValidUrl(form.website))
      errors.push("Invalid website URL");
    const forbiddenSequence = "||";
    if (form.name.includes(forbiddenSequence)) {
      errors.push("Name cannot contain '||'");
    }
    if (form.description.includes(forbiddenSequence)) {
      errors.push("Description cannot contain '||'");
    }
    if (form.logo && form.logo.includes(forbiddenSequence)) {
      errors.push("Logo URL cannot contain '||'");
    }
    if (form.website && form.website.includes(forbiddenSequence)) {
      errors.push("Website URL cannot contain '||'");
    }

    return errors;
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  return (
    <div className="flex justify-center items-start w-full px-4 py-8">
      <div className="w-full max-w-lg flex flex-col gap-y-6 bg-white border-2 border-[#1E1E1E] rounded-3xl p-6 max-sm:p-3 shadow-sm border-b-4 ">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h2 className="text-3xl font-bold text-gray-800">Create Token</h2>

          <a
            href="https://docs.eaglefi.io/token-creation"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 flex items-center gap-1 hover:underline"
          >
            Read Our Guide
            <svg
              className="w-5 h-5"
              viewBox="0 0 16 16"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M4.53 4.75A.75.75 0 0 1 5.28 4h6.01a.75.75 0 0 1 .75.75v6.01a.75.75 0 0 1-1.5 0v-4.2l-5.26 5.261a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734L9.48 5.5h-4.2a.75.75 0 0 1-.75-.75Z" />
            </svg>
          </a>
        </div>

        <p className="text-gray-950">
          Easily create and launch your token in just a few steps.
        </p>

        {/* Form Fields */}
        <form onSubmit={handleSubmit}>
          <div className="flex flex-col gap-4">
            {/* Token Name */}
            <div>
              <label className="block text-gray-950 font-medium mb-2">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="EagleFi"
                value={form.name}
                onChange={(e) => setForm({ ...form, name: e.target.value })}
                className="w-full bg-[#F9FAFB] border-2 border-[#1E1E1E] rounded-lg px-4 py-3 focus:border-[#1E1E1E] focus:outline-none focus:ring-2 focus:ring-[#1E1E1E]/50"
              />
            </div>

            {/* Token Symbol */}
            <div>
              <label className="block text-gray-950 font-medium mb-2">
                Symbol <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="EGL"
                value={form.symbol}
                onChange={(e) => setForm({ ...form, symbol: e.target.value })}
                className="w-full bg-[#F9FAFB] border-2 border-[#1E1E1E] rounded-lg px-4 py-3 focus:border-[#1E1E1E] focus:outline-none focus:ring-2 focus:ring-[#1E1E1E]/50"
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-gray-950 font-medium mb-2">
                Description{" "}
                <span className="text-gray-600 text-sm font-medium">
                  (Optional)
                </span>
              </label>
              <textarea
                placeholder="Describe the story, purpose, vision, or vibes behind your token."
                rows={4}
                value={form.description}
                onChange={(e) =>
                  setForm({ ...form, description: e.target.value })
                }
                className="w-full bg-[#F9FAFB] border-2 border-[#1E1E1E] rounded-lg px-4 py-3 focus:border-[#1E1E1E] focus:outline-none focus:ring-2 focus:ring-[#1E1E1E]/50"
              ></textarea>
            </div>

            {/* Logo Input Section */}
            <div>
              <label className="block text-gray-950 font-medium mb-2">
                Logo URL
              </label>
              <div className="space-y-2">
                <input
                  type="url"
                  placeholder="https://example.com/logo.png"
                  value={form.logo}
                  onChange={(e) => setForm({ ...form, logo: e.target.value })}
                  className="w-full bg-[#F9FAFB] border-2 border-[#1E1E1E] rounded-lg px-4 py-3 focus:border-[#1E1E1E] focus:outline-none focus:ring-2 focus:ring-[#1E1E1E]/50"
                />
                {form.logo && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-950 mb-2">Logo Preview:</p>
                    <img
                      src={form.logo}
                      alt="Logo preview"
                      className="max-w-[200px] max-h-[200px] object-contain rounded-lg  border-2 border-[#1E1E1E]"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = "none";
                      }}
                    />
                  </div>
                )}
                <p className="text-gray-600 text-xs">
                  Recommended size: 256x256 (URL must be accessible)
                </p>
              </div>
            </div>

            {/* Token Features */}
            <div className="space-y-3">
              <h3 className="text-gray-950 font-medium">Token Features</h3>
              <div className="flex flex-wrap gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={form.isPausable}
                    onChange={(e) =>
                      setForm({ ...form, isPausable: e.target.checked })
                    }
                    className="h-5 w-5 cursor-pointer border-2 border-[#1E1E1E] rounded-md checked:bg-[#1E1E1E] focus:ring-[#1E1E1E]"
                  />
                  <span className="text-gray-950">Pausable</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={form.isMintable}
                    onChange={(e) =>
                      setForm({ ...form, isMintable: e.target.checked })
                    }
                    className="h-5 w-5 cursor-pointer border-2 border-[#1E1E1E] rounded-md checked:bg-[#1E1E1E] focus:ring-[#1E1E1E]"
                  />
                  <span className="text-gray-950">Mintable</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={form.isBurnable}
                    onChange={(e) =>
                      setForm({ ...form, isBurnable: e.target.checked })
                    }
                    className="h-5 w-5 cursor-pointer border-2 border-[#1E1E1E] rounded-md checked:bg-[#1E1E1E] focus:ring-[#1E1E1E]"
                  />
                  <span className="text-gray-950">Burnable</span>
                </label>
              </div>
              <p className="text-gray-600 text-xs">
                Pausable: Allows stopping all token transfers
                <br />
                Mintable: Allows creating new tokens after deployment
                <br />
                Burnable: Allows users to destroy their tokens
              </p>
            </div>

            {/* Social Links */}
            <div className="space-y-3">
              {/* <h3 className="text-gray-700 font-medium">Social Links</h3> */}

              <div className="relative">
                <FaGlobe className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                <input
                  type="url"
                  placeholder="Website"
                  value={form.website}
                  onChange={(e) =>
                    setForm({ ...form, website: e.target.value })
                  }
                  className="w-full pl-10 bg-[#F9FAFB] border-2 border-[#1E1E1E] rounded-lg px-4 py-3 focus:border-[#1E1E1E] focus:outline-none focus:ring-2 focus:ring-[#1E1E1E]/50"
                />
              </div>
            </div>

            {/* Supply */}
            <div>
              <label className="block text-gray-950 font-medium mb-2">
                Supply <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                placeholder="100,000,000"
                value={form.supply}
                onChange={(e) => setForm({ ...form, supply: e.target.value })}
                className="w-full bg-[#F9FAFB] border-2 border-[#1E1E1E] rounded-lg px-4 py-3 focus:border-[#1E1E1E] focus:outline-none focus:ring-2 focus:ring-[#1E1E1E]/50"
              />
              <p className="text-gray-600 text-xs mt-1">
                The decimal value for tokens is fixed at 18.
              </p>
            </div>
          </div>

          {/* Connect/Create Wallet Button */}
          <button
            type="submit"
            disabled={isButtonDisabled}
            className={`w-full border-b-[6px] border-2 border-[#000000] ${
              isButtonDisabled
                ? "bg-gray-300 cursor-not-allowed"
                : "bg-[#F6C955] hover:opacity-90"
            } text-[#333333] font-semibold py-3 rounded-xl transition mt-4`}
          >
            {getButtonText()}
          </button>
          <p className="mt-2 text-sm text-gray-600">{status}</p>
        </form>
      </div>
      <RedirectModal
        isOpen={showRedirectModal}
        onClose={() => setShowRedirectModal(false)}
        tokenAddress={newTokenAddress}
        tokenSymbol={tokenName}
      />
    </div>
  );
};

export default CreateTokenForm;
