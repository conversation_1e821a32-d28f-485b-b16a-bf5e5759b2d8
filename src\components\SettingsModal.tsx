import { useState } from "react";

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  slippage: number;
  setSlippage: (value: number) => void;
}

const SettingsModal = ({
  isOpen,
  onClose,
  slippage,
  setSlippage,
}: SettingsModalProps) => {
  const [inputValue, setInputValue] = useState(slippage.toString());
  const [error, setError] = useState<string | null>(null);

  const validateInput = (value: string) => {
    const numValue = parseFloat(value);

    if (isNaN(numValue) || numValue < 0.1 || numValue > 100) {
      setError("Please enter a value between 0.1% and 100%");
      return false;
    }

    if (numValue < 0.5) {
      setError("A very low slippage tolerance may cause transaction failure.");
    } else if (numValue > 5) {
      setError(
        "⚠️ High slippage increases the risk of frontrunning attacks. Proceed with caution."
      );
    } else {
      setError(null);
    }

    return true;
  };

  const handleSave = () => {
    if (!validateInput(inputValue)) return;
    setSlippage(parseFloat(inputValue));
    localStorage.setItem("slippage", inputValue);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl border-2 border-[#1E1E1E] border-b-[6px] border-r-[6px] shadow-xl w-full max-w-md">
        <div className="p-6 border-b-2 border-[#1E1E1E] flex justify-between items-center">
          <h3 className="text-2xl font-bold text-slate-950">
            Transaction Settings
          </h3>
          <button
            onClick={onClose}
            className="text-[#1E1E1E] hover:text-gray-700 text-xl"
          >
            ✕
          </button>
        </div>

        <div className="p-6">
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              <span className="inline-flex items-center gap-2">
                Slippage Tolerance (%)
                <span className="group relative inline-flex">
                  <svg
                    className="w-4 h-4 text-gray-600 cursor-pointer"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <div className="absolute hidden group-hover:block bottom-full left-1/2 -translate-x-1/2 mb-2 w-64 px-3 py-2 text-sm text-gray-600 bg-white border-2 border-[#1E1E1E] rounded-xl shadow-lg">
                    Setting a high slippage tolerance can help transactions
                    succeed, but you may not get such a good price. Use with
                    caution.
                  </div>
                </span>
              </span>
            </label>

            <div className="flex gap-2 mb-3">
              <input
                type="number"
                min="0.1"
                max="100"
                step="0.1"
                value={inputValue}
                onChange={(e) => {
                  setInputValue(e.target.value);
                  validateInput(e.target.value);
                }}
                onBlur={() => validateInput(inputValue)}
                className={`flex-1 p-3 border-2 ${
                  error ? "border-red-500" : "border-[#1E1E1E]"
                } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#F6C955] bg-[#f9fafb]`}
              />
            </div>

            {error && <p className="text-red-500 text-sm mt-1">{error}</p>}

            <div className="flex gap-2 mt-3">
              {[0.1, 0.5, 1, 5].map((value) => (
                <button
                  key={value}
                  onClick={() => {
                    setInputValue(value.toString());
                    setError(null);
                  }}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-all  
                   ${
                     parseFloat(inputValue) === value
                       ? "bg-[#F6C955] text-slate-800"
                       : "bg-[#f9fafb] text-gray-600 hover:bg-[#e3e6f1]"
                   }`}
                  style={{
                    border: "2px solid #1E1E1E",
                    boxShadow:
                      parseFloat(inputValue) === value
                        ? "2px 2px 0 0 #1E1E1E"
                        : "3px 3px 0 0 #1E1E1E",
                  }}
                >
                  {value}%
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="p-6 border-t-2 border-[#1E1E1E] flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-5 py-2.5 bg-gray-200 text-gray-700 hover:bg-gray-100 rounded-lg border-2 border-b-4 border-[#1E1E1E] transition-all"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-5 py-2.5 bg-[#F6C955] text-slate-800 font-medium rounded-lg border-2 border-[#1E1E1E] border-b-4  hover:bg-[#FDE68A] transition-all"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
