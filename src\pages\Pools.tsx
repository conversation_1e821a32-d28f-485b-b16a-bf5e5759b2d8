import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
// import { shortenAddress } from "../lib/utils";
import { useAccountStore } from "@massalabs/react-ui-kit";
import LiquidityModal from "../components/LiquidityModal";
import RefreshBtn from "../components/RefreshBtn";
import axiosInstance from "../lib/axios/axiosInstance";

export interface Token {
  address: string;
  circulated_supply: number;
  created_at: string;
  created_by: string;
  decimals: number;
  description: string;
  is_burnable: boolean;
  is_mintable: boolean;
  is_native: boolean;
  is_pausable: boolean;
  is_paused: boolean;
  logo: string;
  mas_price: number;
  price: {
    mas: number;
    usd: number;
  };
  name: string;
  status: string;
  symbol: string;
  total_supply: number;
}

export interface Pool {
  id: number;
  poolName: string;
  a_token: Token;
  b_token: Token;
  a_reserve: number;
  b_reserve: number;
  total_lp_supply: string;
  logos: string[];
  tokenAddresses: string[];
  pool_address: string;
  input_fee: string;
  user_lp_amount?: number;
  volume_24h: { mas: number; usd: number };
  tvl: { mas: number; usd: number };
  earned_fees_24h: { mas: number; usd: number };
  apr: number;
}

const Pools: React.FC = () => {
  const [initialMode, setInitialMode] = useState<"info" | "add">("info");
  const [selectedPool, setSelectedPool] = useState<Pool | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const navigate = useNavigate();
  const [pools, setPools] = useState<Pool[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { connectedAccount } = useAccountStore();
  const fetchPools = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.get("/pools");
      const backendPools = response.data;

      const transformedPools: Pool[] = backendPools.map(
        (pool: any, index: number) => ({
          id: index + 1,
          poolName: `${pool.a_token.symbol}/${pool.b_token.symbol}`,
          a_token: pool.a_token,
          b_token: pool.b_token,
          a_reserve: pool.a_reserve,
          b_reserve: pool.b_reserve,
          total_lp_supply: pool.total_lp_supply,
          logos: [pool.a_token.logo, pool.b_token.logo],
          tokenAddresses: [pool.a_token.address, pool.b_token.address],
          pool_address: pool.pool_address,
          input_fee: `${pool.input_fee}%`,
          volume_24h: pool.volume_24h,
          tvl: pool.tvl,
          earned_fees_24h: pool.earned_fees_24h,
          apr: pool.apr,
        })
      );
      setPools(transformedPools);
    } catch (err) {
      setError("Failed to fetch pools. Please try again later.");
      console.error("Error fetching pools:", err);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchPools();
  }, []);

  const handleRefresh = async () => {
    await fetchPools();
  };

  const formatUSD = (value: number) => {
    return `$${value.toLocaleString(undefined, { maximumFractionDigits: 2 })}`;
  };

  const CardSkeleton = () => (
    <div className="animate-pulse bg-white p-4 rounded-xl border-2 border-[#1E1E1E] space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div className="flex -space-x-2">
            <div className="w-8 h-8 rounded-full bg-gray-300" />
            <div className="w-8 h-8 rounded-full bg-gray-300" />
          </div>
          <div className="h-6 w-20 bg-gray-300 rounded-md" />
        </div>
        <div className="h-6 w-12 bg-gray-300 rounded-md" />
      </div>
      <div className="space-y-2">
        <div className="h-4 w-full bg-gray-300 rounded-md" />
        <div className="h-4 w-3/4 bg-gray-300 rounded-md" />
        <div className="h-4 w-2/4 bg-gray-300 rounded-md" />
      </div>
      <div className="h-10 w-full bg-gray-300 rounded-lg" />
    </div>
  );

  const SkeletonRow = () => (
    <tr className="animate-pulse">
      <td className="py-3 px-4">
        <div className="flex items-center gap-2">
          <div className="flex -space-x-2">
            <div className="w-7 h-7 rounded-full bg-gray-300"></div>
            <div className="w-7 h-7 rounded-full bg-gray-300"></div>
          </div>
          <div className="h-6 w-12 bg-gray-300 rounded-full"></div>
        </div>
      </td>
      <td className="py-3 px-4">
        <div className="h-6 w-24 bg-gray-300 rounded-md"></div>
      </td>
      <td className="py-3 px-4">
        <div className="h-6 w-24 bg-gray-300 rounded-md"></div>
      </td>
      <td className="py-3 px-4">
        <div className="h-6 w-24 bg-gray-300 rounded-md"></div>
      </td>
      <td className="py-3 px-4">
        <div className="h-6 w-16 bg-gray-300 rounded-md"></div>
      </td>
      <td className="py-3 px-4">
        <div className="h-9 w-16 bg-gray-300 rounded-lg"></div>
      </td>
    </tr>
  );

  return (
    <main className="container mx-auto px-4 md:px-6 lg:px-8 my-8">
      <div className="bg-white border-2 border-[#1E1E1E] rounded-3xl p-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h2 className="text-3xl font-bold text-slate-950 mb-1">Pools</h2>
            <span className="text-sm text-stone-800">
              Provide liquidity and earn rewards.
            </span>
          </div>

          <div className="flex flex-col md:flex-row md:items-center gap-4">
            <div className="flex-1 flex flex-col md:flex-row md:items-center gap-4 relative w-full max-w-[400px]">
              {/* <div className="flex items-center rounded-md px-3 py-2.5 border-2 border-[#1E1E1E] bg-white relative w-full">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  data-prefix="fas"
                  data-icon="magnifying-glass"
                  className="w-4 h-4 text-gray-500 mr-2"
                  role="img"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                >
                  <path
                    fill="currentColor"
                    d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0s208 93.1 208 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"
                  />
                </svg>
                <input
                  type="text"
                  placeholder="Search pools..."
                  className="bg-transparent w-full text-sm text-[#1E1E1E] placeholder:text-gray-500 focus:outline-none"
                />
              </div> */}
            </div>

            <div className="flex flex-col md:flex-row md:items-center gap-4">
              <div className="hidden md:block">
                <RefreshBtn onClick={handleRefresh} />
              </div>
              <button
                onClick={() => navigate("/create-pool")}
                className="bg-[#F6C955] text-[#333333] font-medium px-4 py-2 rounded-lg border-2 border-[#1E1E1E] border-b-4 border-r-4 hover:bg-[#FDE68A] transition-all"
              >
                Create Pool
              </button>
            </div>
          </div>
        </div>
        {/* Desktop Table View */}
        <div className="hidden md:block overflow-x-auto rounded-xl border-2 border-[#000000] border-b-[6px] border-r-[6px]">
          <table className="w-full table-auto text-sm bg-[#F6F6F6]">
            <thead className="bg-[#E6EBEC]">
              <tr>
                <th className="py-3 px-4 text-left text-[#1E1E1E] font-semibold">
                  Pool
                </th>

                <th className="py-3 px-4 text-left text-[#1E1E1E] font-semibold">
                  TVL
                </th>
                <th className="py-3 px-4 text-left text-[#1E1E1E] font-semibold">
                  Volume (24h)
                </th>
                <th className="py-3 px-4 text-left text-[#1E1E1E] font-semibold">
                  Fees (24h)
                </th>
                <th className="py-3 px-4 text-left text-[#1E1E1E] font-semibold">
                  APR
                </th>
                <th className="py-3 px-4 text-left text-[#1E1E1E] font-semibold">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-[#F6F6F6]">
              {loading ? (
                <>
                  {Array.from({ length: 5 }).map((_, index) => (
                    <SkeletonRow key={index} />
                  ))}
                </>
              ) : error ? (
                <tr>
                  <td
                    colSpan={6}
                    className="py-6 px-4 text-center text-red-500"
                  >
                    {error}
                  </td>
                </tr>
              ) : pools.length > 0 ? (
                pools.map((pool) => (
                  <tr
                    key={pool.id}
                    className="hover:bg-[#E6EBEC] border-b border-b-[#2B2C3B] ease-in-out duration-200 cursor-pointer"
                    onClick={() => {
                      // if (!connectedAccount) return;
                      setSelectedPool(pool);
                      setInitialMode("info");
                      setIsModalOpen(true);
                    }}
                  >
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <div className="flex -space-x-2">
                          {pool.logos.map((logoUrl, idx) => {
                            const showLogo = logoUrl && logoUrl.trim() !== "";
                            const fallbackText = pool.poolName
                              .slice(0, 4)
                              .toUpperCase();

                            return showLogo ? (
                              <img
                                key={idx}
                                src={logoUrl}
                                alt={`token${idx}`}
                                className="w-7 h-7 rounded-full border border-white"
                                onError={(e) => {
                                  const imgElement =
                                    e.target as HTMLImageElement;
                                  const fallbackDiv =
                                    document.createElement("div");
                                  fallbackDiv.className =
                                    "w-7 h-7 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold border border-white";
                                  fallbackDiv.textContent = fallbackText;
                                  imgElement.replaceWith(fallbackDiv);
                                }}
                              />
                            ) : (
                              <div
                                key={idx}
                                className="w-7 h-7 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold border border-white"
                              >
                                {fallbackText}
                              </div>
                            );
                          })}
                        </div>
                        <div className="flex flex-col">
                          <span className="font-medium text-[#1E1E1E]">
                            {pool.poolName}
                          </span>
                        </div>
                        <div
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 cursor-help"
                          title="Swap fee percentage for this pool"
                        >
                          {pool.input_fee}
                        </div>
                      </div>
                    </td>

                    <td className="py-3 px-4 text-[#1E1E1E]">
                      {formatUSD(pool.tvl.usd)}
                    </td>
                    <td className="py-3 px-4 text-[#1E1E1E]">
                      {formatUSD(pool.volume_24h.usd)}
                    </td>
                    <td className="py-3 px-4 text-[#1E1E1E]">
                      {formatUSD(pool.earned_fees_24h.usd)}
                    </td>
                    <td className="py-3 px-4 text-[#1E1E1E]">
                      {pool.apr.toFixed(2)}%
                    </td>
                    <td className="py-3 px-4 text-[#1E1E1E]">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedPool(pool);
                          setInitialMode("add");
                          setIsModalOpen(true);
                        }}
                        className={`${
                          connectedAccount
                            ? "bg-[#F6C955] hover:bg-[#FDE68A]"
                            : "bg-gray-300 cursor-not-allowed"
                        } text-[#333333] px-3 py-1 rounded-lg border-2 border-[#1E1E1E] border-b-4 border-r-4 transition-all`}
                        disabled={!connectedAccount}
                      >
                        Add
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={6}
                    className="py-6 px-4 text-center text-slate-600"
                  >
                    No pools available. Click "Create Pool" to add liquidity.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {/* Mobile Card View */}
        <div className="md:hidden space-y-4">
          {loading ? (
            Array.from({ length: 3 }).map((_, index) => (
              <CardSkeleton key={index} />
            ))
          ) : error ? (
            <div className="text-center py-6 text-red-500">{error}</div>
          ) : pools.length > 0 ? (
            pools.map((pool) => (
              <div
                key={pool.id}
                className="bg-white p-4 rounded-xl border-2 border-[#1E1E1E] hover:bg-[#F6F6F6] transition-colors cursor-pointer"
                onClick={() => {
                  if (!connectedAccount) return;
                  setSelectedPool(pool);
                  setInitialMode("info");
                  setIsModalOpen(true);
                }}
              >
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-3">
                    <div className="flex -space-x-2">
                      {pool.logos.map((logoUrl, idx) => {
                        const showLogo = logoUrl && logoUrl.trim() !== "";
                        const fallbackText = pool.poolName
                          .slice(0, 4)
                          .toUpperCase();

                        return showLogo ? (
                          <img
                            key={idx}
                            src={logoUrl}
                            alt={`token${idx}`}
                            className="w-7 h-auto rounded-full border border-white"
                          />
                        ) : (
                          <div
                            key={idx}
                            className="w-7 h-auto flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold border border-white"
                          >
                            {fallbackText}
                          </div>
                        );
                      })}
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{pool.poolName}</h3>
                      <span className="text-sm text-gray-600">
                        Fee: {pool.input_fee}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">TVL</span>
                    <span className="font-medium">
                      {formatUSD(pool.tvl.usd)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Volume (24h)</span>
                    <span className="font-medium">
                      {formatUSD(pool.volume_24h.usd)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">APR</span>
                    <span className="font-medium">{pool.apr.toFixed(2)}%</span>
                  </div>
                </div>

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedPool(pool);
                    setInitialMode("add");
                    setIsModalOpen(true);
                  }}
                  className={`w-full ${
                    connectedAccount
                      ? "bg-[#F6C955] hover:bg-[#FDE68A]"
                      : "bg-gray-300 cursor-not-allowed"
                  } text-[#333333] py-2 rounded-lg border-2 border-[#1E1E1E] border-b-4 border-r-4 transition-all`}
                  disabled={!connectedAccount}
                >
                  Add Liquidity
                </button>
              </div>
            ))
          ) : (
            <div className="text-center py-6 text-gray-600">
              No pools available. Click "Create Pool" to add liquidity.
            </div>
          )}
        </div>
      </div>

      {isModalOpen && selectedPool && (
        <LiquidityModal
          pool={selectedPool}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedPool(null);
          }}
          initialMode={initialMode}
          isConnected={!!connectedAccount}
          // onRequestConnect={() => {
          //   console.log("connect wallet");
          // }}
        />
      )}
    </main>
  );
};

export default Pools;
