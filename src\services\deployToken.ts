import {
  Args,
  Mas,
  parseMas,
  parseUnits,
  SmartContract,
  U8,
  OperationStatus,
} from "@massalabs/massa-web3";
import { logError } from "../lib/utils2";

const TOKEN_DEPLOYER_ADDRESS = import.meta.env
  .VITE_TOKEN_DEPLOYER_CONTRACT_ADDRESS;

const TOKEN_EXECUTION_MODE =
  import.meta.env.VITE_TOKEN_DEPLOYMENT_EXECUTION_MODE || "final";

async function waitForTokenExecution(
  operation: any
): Promise<[number, boolean]> {
  if (TOKEN_EXECUTION_MODE === "final") {
    const status = await operation.waitFinalExecution();
    return [status, status === OperationStatus.Success];
  } else {
    const status = await operation.waitSpeculativeExecution();
    return [status, status === OperationStatus.SpeculativeSuccess];
  }
}

async function getTokenOperationEvents(operation: any): Promise<any[]> {
  if (TOKEN_EXECUTION_MODE === "final") {
    return await operation.getFinalEvents();
  } else {
    return await operation.getSpeculativeEvents();
  }
}

export async function deployToken(provider: any, form: any) {
  try {
    const tokenDeployer = new SmartContract(provider, TOKEN_DEPLOYER_ADDRESS);
    console.log("tokendeployer", tokenDeployer);
    const args = new Args()
      .addString(form.name)
      .addString(form.symbol)
      .addU8(U8.fromNumber(18))
      .addU256(parseUnits(form.supply, 18))
      .addString(form.logo)
      .addString(form.website)
      .addString(form.description)
      .addBool(form.isPausable)
      .addBool(form.isMintable)
      .addBool(form.isBurnable)
      .addU64(parseMas("8"));

    const operation = await tokenDeployer.call("createNewToken", args, {
      coins: Mas.fromString("20"),
    });

    const [, isSuccess] = await waitForTokenExecution(operation);
    console.log("operation", operation);

    const events = await getTokenOperationEvents(operation);

    console.log("Events : ", events);

    const lastEvent = events.at(-2);

    console.log("BeforeLast Event : ", lastEvent);

    if (isSuccess) {
      console.log("Token deployed successfully");

      let tokenAddress = "";

      if (lastEvent) {
        const inputString = lastEvent.data;
        console.log("Event Data : ", inputString);
        const parts = inputString.split("||");
        if (parts.length >= 3) {
          tokenAddress = parts[2];
          console.log("Token Address:", tokenAddress);
        } else {
          console.log("Token Address not found: unexpected event format");
        }
      }

      return {
        name: form.name,
        symbol: form.symbol,
        address: tokenAddress,
        decimals: 18,
        logo: form.logo,
        price: 0,
        balance: form.supply,
        is_native: false,
        operationId: operation.id,
      };
    } else {
      const errorEvent = events.find((event) =>
        event.data.includes("massa_execution_error")
      );
      const errorMessage = errorEvent
        ? JSON.parse(errorEvent.data).massa_execution_error
        : "Unknown error occurred";
      logError(errorMessage, operation.id, provider?.address || null).catch(
        (e) => console.error("Failed to log error", e)
      );
      throw new Error("Token deployment failed");
    }
  } catch (error) {
    console.error("Error deploying token:", error);
    throw error;
  }
}
