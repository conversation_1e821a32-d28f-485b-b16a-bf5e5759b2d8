import React, { useState } from "react";
import { useImmer } from "use-immer";
import ChatInput from "./ChatInput";
import ChatMessages from "./ChatMessages";
import { Message } from "../types";
import { useAccountStore } from "@massalabs/react-ui-kit";

const Chatbot: React.FC = () => {
  const [chatId, setChatId] = useState<string | null>(null);
  const [messages, setMessages] = useImmer<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");

  const { connectedAccount } = useAccountStore();
  const userAddress = connectedAccount?.address || "unknown";

  const isLoading =
    messages.length > 0
      ? Boolean(messages[messages.length - 1].loading)
      : false;

  async function submitNewMessage() {
    const trimmedMessage = newMessage.trim();
    if (!trimmedMessage || isLoading) return;

    setMessages((draft) => {
      draft.push({ role: "user", content: trimmedMessage });
      draft.push({ role: "model", content: "", loading: true });
    });
    setNewMessage("");

    try {
      const response = await fetch(`${import.meta.env.VITE_AI_API_URL}/chat`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          message: trimmedMessage,
          user_address: userAddress,
          network: import.meta.env.VITE_AI_NETWORK || "buildnet",
          ...(chatId && { chat_id: chatId }),
        }),
      });

      if (!response.ok) throw new Error("Request failed");

      const data = await response.json();

      setMessages((draft) => {
        const lastMessage = draft[draft.length - 1];
        lastMessage.content = data.response;
        lastMessage.loading = false;
      });

      if (!chatId) setChatId(data.chat_id);
    } catch (err) {
      setMessages((draft) => {
        const lastMessage = draft[draft.length - 1];
        lastMessage.loading = false;
        lastMessage.error = true;
      });
    }
  }

  return (
    <div className="relative grow flex flex-col gap-6">
      {messages.length === 0 && (
        <div className="mt-3 text-gray-500 text-base space-y-2">
          <p className="mb-1 text-2xl font-bold text-slate-700">
            Hey there, welcome to EagleAI!
          </p>
          <div className="bg-amber-50 border border-amber-200 rounded-md p-3 mt-3 text-amber-800 text-sm">
            <span className="font-semibold">Beta Version Notice:</span> EagleAI
            is currently in beta. You may encounter some issues while using it.
            We appreciate your patience and feedback as we improve the service.
          </div>
          <p>
            Let’s soar to new heights together, what can I help you with today?
            🚀
          </p>

          <img
            src="/images/big logo 2.svg"
            className="pointer-events-none select-none mx-auto w-[298px] h-auto px-[90px] hidden md:block"
            alt="Header banner"
          />
        </div>
      )}

      <ChatMessages messages={messages} isLoading={isLoading} />
      <ChatInput
        newMessage={newMessage}
        isLoading={isLoading}
        setNewMessage={setNewMessage}
        submitNewMessage={submitNewMessage}
      />
    </div>
  );
};

export default Chatbot;
