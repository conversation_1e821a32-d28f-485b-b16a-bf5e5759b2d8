/* @import url("https://fonts.googleapis.com/css2?family=Urbanist:wght@100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap"); */

@tailwind base;
@tailwind components;
@tailwind utilities;

a {
  color: inherit;
  text-decoration: inherit;
  font-weight: 700;
}

.bg-maintenance {
  background-image: url("/images/sky-bg.jpg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

body {
  /* background: linear-gradient(139.73deg, #e5fdff, #f3efff); */
  background-image: url("/images/sky-bg.jpg");
  /* background-image: url("/images/landing bg-2.jpg"); */
  /* background: url('data:image/svg+xml,<svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><filter id="n"><feTurbulence type="fractalNoise" baseFrequency="0.005" numOctaves="3"/></filter><rect width="100%" height="100%" filter="url(%23n)" opacity="0.4"/></svg>'),
    linear-gradient(139.73deg, #e5fdff, #f3efff); */
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  /* background-attachment: fixed; */

  cursor: url("/images/cursor.png") 0 0, auto;
  font-family: "DM Sans", sans-serif;
}

tbody > tr {
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: 0.5px;
  font-weight: 500;
  border-radius: 1.5rem;
  padding: 20px 24px;
}

tbody > tr > td {
  padding: 20px 24px;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}
.button-shadow {
  box-shadow: 0 16px 64px #6801ff42;
}

.container {
  max-width: 1440px;
  margin: auto;
}

.main-content {
  padding: 1.5rem 1rem;
}

.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}

.play::after {
  content: "";
  border-radius: 100%;
  background: "#31d0aa";
  height: 8px;
  width: 8px;
  margin-left: 12px;
}

.dropdown {
  position: relative;
  display: inline-block;
  z-index: 500;
}

.dropdown-content {
  display: none;
  position: absolute;
  min-width: 200px;
  z-index: 1;
}

.radial-gradient-with-background {
  background: radial-gradient(#08062d99, #14133d7a 50%),
    var(--body-background-color);
}
.dropdown-content a {
  padding: 16px;
  text-decoration: none;
  display: block;
}
.dropdown-content a:hover {
  --tw-bg-opacity: 0.5;
  background-color: rgb(202 215 236 / var(--tw-bg-opacity));
}

.dropdown:hover .dropdown-content {
  display: block;
}

eagle-dropdown .eagle-dropdown {
  scrollbar-width: thin;
  scrollbar-color: rgb(202 215 236 /1) #e3e6f1;
}

.eagle-dropdown::-webkit-scrollbar {
  width: 8px;
}

.eagle-dropdown::-webkit-scrollbar-track {
  background: #e3e6f1;
  border-radius: 4px;
}

.eagle-dropdown::-webkit-scrollbar-thumb {
  background: rgb(202 215 236 /1);
  border-radius: 4px;
}

.eagle-dropdown::-webkit-scrollbar-thumb:hover {
  background: #6e7aaa; /* slightly darker on hover */
}

@keyframes spin-fast {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(180deg);
  }
}
.animate-spin-fast {
  animation: spin-fast 0.3s linear infinite;
}

/* for ai chatbot */

@layer base {
  /* body,
  html,
  #root {
    @apply text-main-text h-full;
  } */

  ::-webkit-scrollbar {
    @apply h-4 w-2;
  }

  ::-webkit-scrollbar:horizontal {
    @apply h-4 w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent rounded;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary-blue/50 border border-white rounded;
  }
}

@layer components {
  .spinner-child {
    @apply absolute -top-1 left-0 h-full w-full content-[''] rounded-full bg-black/10 opacity-0;
  }

  .markdown-container p {
    @apply mb-5;
  }

  .markdown-container h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-bold mb-5;
  }

  .markdown-container li {
    @apply mb-3;
  }

  .markdown-container li:last-child {
    @apply mb-0;
  }

  .markdown-container ol {
    @apply list-decimal mx-0 mt-0 mb-5 pl-8;
  }

  .markdown-container ul {
    @apply list-disc mx-0 mt-0 mb-5 pl-8;
  }

  .markdown-container > :last-child {
    @apply mb-0;
  }
}

/* end ai chatbot */

.number.increase.animating {
  animation: flashIncrease 0.5s ease-in-out;
}
@keyframes flashIncrease {
  0%,
  100% {
    color: inherit;
  }
  50% {
    color: #22c55e;
  } /* Green for increase */
}

.number.decrease.animating {
  animation: flashDecrease 0.5s ease-in-out;
}
@keyframes flashDecrease {
  0%,
  100% {
    color: inherit;
  }
  50% {
    color: #ef4444;
  } /* Red for decrease */
}

/* .number.no-change.animating {
  animation: flashNoChange 0.5s ease-in-out;
} */
/* Blue for no change */
/* @keyframes flashNoChange {
  0%,
  100% {
    color: inherit;
  }
  50% {
    color: #3b82f6;
  }
} */

@media screen and (min-width: 1280px) {
  .sidebar-right-container {
    position: sticky;
    top: 110px;
    height: calc(100vh - 100px);
    overflow-y: auto;
  }
}

@keyframes smoothUpdate {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 1;
  }
}

.updating-value {
  animation: smoothUpdate 1s ease-in-out;
}
@keyframes slideUp {
  0%,
  26.67% {
    transform: translateY(0);
  }
  33.33% {
    transform: translateY(-7rem);
  }
  60%,
  66.67% {
    transform: translateY(-7rem);
  }
  73.33% {
    transform: translateY(-14rem);
  }
  90%,
  96.67% {
    transform: translateY(-14rem);
  }
  100% {
    transform: translateY(-17rem);
  }
}

.slide-font {
  font-family: "Climate Crisis", sans-serif;
  font-weight: 400;
  font-style: normal;
}

/* react tostify customization  */
:root {
  --toastify-color-info: #f6c955;
  --toastify-color-success: #166534;
  --toastify-color-warning: #ffa500;
  --toastify-color-error: #b91c1c;
  --toastify-toast-width: 424px;
  --toastify-font-family: "DM Sans", sans-serif;
}

.Toastify__toast-container {
  max-width: 90vw !important;
}

.Toastify__toast {
  border: 2px solid #1e1e1e !important;
  border-bottom-width: 4px !important;
  border-right-width: 4px !important;
  border-radius: 12px !important;
  box-shadow: -8px 8px 16px 0px rgba(133, 133, 133, 0.15) !important;
  font-family: "DM Sans", sans-serif !important;
  font-weight: 700 !important;
  color: #1e1e1e !important;
  padding: 16px !important;
  margin-bottom: 1rem !important;
  background: #f6f6f6 !important;
}

.Toastify__toast-theme--colored.Toastify__toast--info {
  background: #fffbe6 !important;
  border-color: #1e1e1e !important;
}

.Toastify__toast-theme--colored.Toastify__toast--success {
  background: #dcfce7 !important;
  border-color: #1e1e1e !important;
}

.Toastify__toast-theme--colored.Toastify__toast--warning {
  background: #fff4e5 !important;
  border-color: #1e1e1e !important;
}

.Toastify__toast-theme--colored.Toastify__toast--error {
  background: #fef2f2 !important;
  border-color: #1e1e1e !important;
}

.Toastify__close-button {
  color: #1e1e1e !important;
  align-self: flex-start !important;
}

.Toastify__toast-icon {
  margin-right: 12px !important;
}

.Toastify__toast-body {
  align-items: flex-start !important;
  padding: 0 !important;
}

.Toastify__progress-bar {
  background: rgba(30, 30, 30, 0.2) !important;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

/* react ui kit connect wallet modal */
#options {
  border: 2px solid #1e1e1e;
  border-radius: 6px;
  padding-top: 0;
  padding-bottom: 0;
}

#options > ul > li {
  border-bottom: 2px solid #1e1e1e;
  border-radius: 0px;
}
#options > ul > li > div {
  border-radius: 0px;
}

#dropdownUiKitButton {
  border: 2px solid #1e1e1e;
  border-radius: 6px;
}

/* ////// blog post ////////// */

.markdown-content {
  line-height: 1.6;
}

.markdown-content table {
  @apply w-full my-6 border-collapse border border-[#1E1E1E];
}

.markdown-content th,
.markdown-content td {
  @apply p-3 border border-[#1E1E1E] text-left;
}

.markdown-content blockquote {
  @apply border-l-4 border-[#F6C955] pl-4 my-4 italic text-gray-600;
}

.markdown-content img {
  @apply my-6 rounded-lg border-2 border-[#1E1E1E];
}

/* ////// Floppy eagle////////// */
.game-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 2rem;
}

.game-header {
  display: flex;
  align-items: center;
  margin-right: 10px;
  margin-left: 10px;
  background-color: white;
  padding: 1rem;
  margin-bottom: 1rem;
  text-align: center;
}

.game-logo {
  width: 100px;
  height: auto;
  margin-bottom: 0.5rem;
}

.game-title {
  font-size: 2rem;
  font-weight: bold;
  color: #1e1e1e;
  margin-bottom: 0.25rem;
}

.game-subtitle {
  font-size: 1rem;
  color: #333333;
}

.game-container {
  position: relative;
  overflow: hidden;
  margin: 0 auto; /* Center the container */
}

.game-over-message {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent gray overlay */
  color: white;
  font-size: 24px;
  text-align: center;
}

.game-over-message h2 {
  font-size: 36px;
  margin-bottom: 10px;
}

.game-over-message p {
  margin-bottom: 20px;
}

.game-over-message button {
  padding: 10px 20px;
  font-size: 18px;
  background-color: #4caf50; /* Green button */
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.game-over-message button:hover {
  background-color: #45a049; /* Slightly darker green on hover */
}

@media (max-width: 600px) {
  .game-header {
    padding: 0.5rem;
  }
  .game-title {
    font-size: 1.5rem;
  }
  .game-subtitle {
    font-size: 0.875rem;
  }
  .game-logo {
    width: 60px;
  }
}

canvas {
  position: absolute;
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-crisp-edges;
  image-rendering: pixelated;
  image-rendering: crisp-edges;
}
.tutorial {
  margin: 15px 0;
  font-size: 18px;
  line-height: 1.4;
}

.start-button {
  padding: 10px 20px;
  font-size: 18px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 15px;
}

.start-button:hover {
  background-color: #45a049;
}

@media (max-width: 600px) {
  .game-over-message h2 {
    font-size: 28px;
  }

  .tutorial {
    font-size: 16px;
    padding: 0 15px;
  }

  .start-button {
    font-size: 16px;
    padding: 8px 16px;
  }
}
