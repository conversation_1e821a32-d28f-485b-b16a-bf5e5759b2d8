import { useParams } from "react-router-dom";
import { Token } from "../redux/features/tokensSlice";
import { useEffect, useState } from "react";
import { DownArrowIcon, UpArrowIcon } from "../components/TokensListTable";
import SwapWidget from "../components/SwapWidget";
import { formatLargeNumber } from "../components/TokensListTable";
import axiosInstance from "../lib/axios/axiosInstance";
import AddressWithCopy from "../components/AddressWithCopy";
import { FaGlobe } from "react-icons/fa";
import { formatCompactTime, getTradeTypeInfo } from "../lib/utils";
import { formatDistanceToNow } from "date-fns";
import { Pool as PoolsPool } from "./Pools";
import LiquidityModal from "../components/LiquidityModal";
import { useAccountStore } from "@massalabs/react-ui-kit";
import IncrementalTradingChart, {
  Timeframe,
} from "../components/IncrementalTradingChart";
import { useNavigate } from "react-router-dom";
import TokenLogo from "../components/TokenLogo";
import { prettyPrice } from "../lib/utils2";

interface Pool {
  id: string;
  token0: {
    symbol: string;
    logo: string;
    address: string;
  };
  token1: {
    symbol: string;
    logo: string;
    address: string;
  };
  liquidity: string;
  volume24h: string;
  volume7d: string;
  APR: string;
  poolAddress: string;
  feeTier: string;
}

const TokenStats = () => {
  const { connectedAccount, currentWallet } = useAccountStore();
  const navigate = useNavigate();
  const { tokenAddress } = useParams<{ tokenAddress: string }>();

  const [activeTab, setActiveTab] = useState<"chart" | "pools" | "info">(
    "chart"
  );
  const [tokenData, setTokenData] = useState<Token | null>(null);
  const [pools, setPools] = useState<Pool[]>([]);
  const [trades, setTrades] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const TRADES_PER_PAGE = 20;

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPool, setSelectedPool] = useState<PoolsPool | null>(null);
  const [initialMode, setInitialMode] = useState<"info" | "add">("info");

  const [selectedTimeframe, setSelectedTimeframe] = useState<Timeframe>("24h");

  const [network, setNetwork] = useState<string | null>(null);

  /* ---------- fetch network ---------- */
  useEffect(() => {
    const fetchNetworkInfo = async () => {
      if (connectedAccount && currentWallet) {
        const networkInfo = await currentWallet.networkInfos();
        const networkName = networkInfo?.name ?? null;
        setNetwork(networkName);
      } else {
        setNetwork(null);
      }
    };

    fetchNetworkInfo();
    // Listen for network changes
    const handleNetworkChange = async () => {
      await fetchNetworkInfo();
    };

    currentWallet?.listenNetworkChanges(handleNetworkChange);
  }, [connectedAccount, currentWallet]);

  /* ---------- token stats polling ---------- */
  useEffect(() => {
    if (!tokenAddress) return;
    const fetchTokenData = async () => {
      try {
        const { data } = await axiosInstance.get(
          `/tokens/${tokenAddress}/stats`
        );
        setTokenData(data);
      } catch (e) {
        console.error(e);
      }
    };
    fetchTokenData();
    const id = setInterval(fetchTokenData, 5_000);
    return () => clearInterval(id);
  }, [tokenAddress]);

  /* ---------- trades (paginated + polling) ---------- */
  useEffect(() => {
    if (!tokenAddress) return;
    const fetchTrades = async () => {
      try {
        const offset = (currentPage - 1) * TRADES_PER_PAGE;
        const { data } = await axiosInstance.get(
          `/tokens/${tokenAddress}/trades`,
          { params: { offset, limit: TRADES_PER_PAGE } }
        );
        setTrades(data.data);
        setHasMore(data.data.length === TRADES_PER_PAGE);
      } catch (e) {
        console.error(e);
      }
    };
    fetchTrades();
    const id = setInterval(fetchTrades, 5_000);
    return () => clearInterval(id);
  }, [tokenAddress, currentPage]);

  /* ---------- pools polling ---------- */
  useEffect(() => {
    if (!tokenAddress) return;
    const fetchPools = async () => {
      try {
        const { data } = await axiosInstance.get(
          `/tokens/${tokenAddress}/pools`
        );
        const mapped = data.map((pool: any) => ({
          ...pool,
          id: pool.pool_address,
          token0: {
            symbol: pool.a_token.symbol,
            logo: pool.a_token.logo,
            address: pool.a_token.address,
          },
          token1: {
            symbol: pool.b_token.symbol,
            logo: pool.b_token.logo,
            address: pool.b_token.address,
          },
          liquidity: `$${formatLargeNumber(pool.tvl.usd)}`,
          volume24h: `$${formatLargeNumber(pool.volume_24h.usd)}`,
          APR: `${pool.apr.toFixed(2)}%`,
          poolAddress: pool.pool_address,
          feeTier: `${pool.input_fee}%`,
        }));
        setPools(mapped);
      } catch (e) {
        console.error(e);
      }
    };
    fetchPools();
    const id = setInterval(fetchPools, 5_000);
    return () => clearInterval(id);
  }, [tokenAddress]);

  /* ---------- helpers ---------- */
  const handlePoolClick = (poolItem: any) => {
    const transformed: PoolsPool = {
      id: poolItem.pool_address,
      poolName: `${poolItem.a_token.symbol}/${poolItem.b_token.symbol}`,
      a_token: poolItem.a_token,
      b_token: poolItem.b_token,
      a_reserve: poolItem.a_reserve,
      b_reserve: poolItem.b_reserve,
      total_lp_supply: poolItem.total_lp_supply,
      logos: [poolItem.a_token.logo, poolItem.b_token.logo],
      tokenAddresses: [poolItem.a_token.address, poolItem.b_token.address],
      pool_address: poolItem.pool_address,
      input_fee: `${poolItem.input_fee}%`,
      volume_24h: poolItem.volume_24h,
      tvl: poolItem.tvl,
      earned_fees_24h: poolItem.earned_fees_24h || { mas: 0, usd: 0 },
      apr: poolItem.apr,
      user_lp_amount: 0,
    };
    setSelectedPool(transformed);
    setInitialMode("info");
    setIsModalOpen(true);
  };

  const formatTokenAmount = (amount: number, symbol: string) =>
    Number.isInteger(amount)
      ? `${amount.toLocaleString()} ${symbol}`
      : `${amount.toLocaleString(undefined, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })} ${symbol}`;

  /* ---------- UI ---------- */
  return (
    <div className="flex flex-col xl:grid xl:grid-cols-12 2xl:grid-cols-8 gap-4 p-4 sm:p-5">
      {/* ------------- main column ------------- */}
      <div className="bg-white rounded-xl border-2 border-[#000] border-b-[8px] border-r-[6px] p-4 sm:p-6 col-start-1 2xl:col-start-0 col-span-9">
        {/* header */}
        <div className="flex flex-col md:flex-row items-start md:items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
          <div className="flex items-center gap-3 sm:gap-4">
            {tokenData?.logo ? (
              <div className="relative">
                <TokenLogo
                  token={tokenData}
                  width="w-12"
                  height="h-12"
                  showBadge={true}
                  className="shadow-md sm:w-16 sm:h-16"
                />
              </div>
            ) : (
              // <img
              //   src={tokenData.logo}
              //   alt={tokenData.symbol}
              //   className="w-12 h-12 sm:w-16 sm:h-16 rounded-full border-2 border-white shadow-md"
              // />
              <div className="w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center rounded-full bg-slate-300 text-white text-xl sm:text-2xl font-semibold">
                {tokenData?.symbol?.slice(0, 3)}
              </div>
            )}
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-slate-700">
                {tokenData?.name}
              </h1>
              <div className="text-lg sm:text-xl text-gray-500">
                {tokenData?.symbol}
              </div>
            </div>
          </div>
        </div>

        {/* nav */}
        <div className="flex flex-col sm:flex-row justify-between border-b border-[#e3e6f1] mb-4 sm:mb-6">
          <AddressWithCopy address={tokenAddress ?? ""} />
          <div className="flex overflow-x-auto pb-2">
            {(["chart", "pools", "info"] as const).map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`pb-4 px-4 sm:px-6 ${
                  activeTab === tab
                    ? "border-b-2 border-blue-600 text-blue-600"
                    : "text-gray-500 hover:text-blue-600"
                } font-bold transition-colors text-sm sm:text-base`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* --------------------- CHART TAB -------------------- */}
        {activeTab === "chart" && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6">
              <StatCard
                label="Price"
                value={prettyPrice(tokenData?.price_usd, "USD", 4, true)}
                secondary={`24h ${
                  tokenData?.price_24h_percentage_change?.toFixed(1) ?? "0"
                }%`}
                isPositive={(tokenData?.price_24h_percentage_change ?? 0) >= 0}
              />
              <StatCard
                label="Volume 24h"
                value={`$${formatLargeNumber(tokenData?.volume_24h_usd ?? 0)}`}
              />
              <StatCard
                label="Market Cap"
                value={`$${formatLargeNumber(tokenData?.market_cap_usd ?? 0)}`}
              />
              <StatCard
                label="Token Liquidity"
                value={`$${formatLargeNumber(
                  tokenData?.total_liquidity_usd ?? 0
                )}`}
              />
            </div>

            {/* timeframe buttons */}
            <div className="flex flex-wrap gap-2 mb-4">
              {(["1h", "4h", "24h", "1mo"] as const).map((tf) => (
                <button
                  key={tf}
                  onClick={() => setSelectedTimeframe(tf)}
                  className={`px-3 py-1.5 rounded-lg border-2 ${
                    selectedTimeframe === tf
                      ? "border-[#1E1E1E] bg-[#F6C955] border-b-4 border-r-4"
                      : "border-[#1E1E1E] hover:bg-[#E6EBEC]"
                  } transition-all`}
                >
                  {tf}
                </button>
              ))}
            </div>

            {/* the chart */}
            <div className="rounded-xl border-2 border-[#e3e6f1] p-2 sm:p-4 h-64 sm:h-80 md:h-96 bg-[#374151]">
              {tokenAddress && (
                <IncrementalTradingChart
                  tokenAddress={tokenAddress}
                  timeframe={selectedTimeframe}
                  className="w-full h-full"
                />
              )}
            </div>

            {/* swap widget mobile */}
            <div className="flex flex-col gap-6 mt-4 xl:hidden">
              <SwapWidget defaultToTokenAddress={tokenData?.address} />
            </div>

            {/* trades table (responsive) */}
            <div className="my-8">
              {/* Scrollable container for all screens */}
              <div className="overflow-x-auto rounded-xl border-2 border-[#000] border-b-[6px] border-r-[6px]">
                <table className="w-full min-w-[550px] bg-[#F6F6F6]">
                  <thead className="bg-[#E6EBEC]">
                    <tr>
                      <th className="py-3 px-2 text-left text-slate-900 font-semibold whitespace-nowrap w-[75px] sm:w-auto">
                        Time
                      </th>
                      <th className="py-3 px-2 text-left text-slate-900 font-semibold whitespace-nowrap">
                        Type
                      </th>
                      <th className="py-3 px-2 text-left text-slate-900 font-semibold whitespace-nowrap">
                        Amount
                      </th>
                      <th className="py-3 px-2 text-left text-slate-900 font-semibold whitespace-nowrap">
                        Price
                      </th>
                      <th className="py-3 px-2 text-left text-slate-900 font-semibold whitespace-nowrap">
                        Total
                      </th>
                      <th className="py-3 px-2 text-left text-slate-900 font-semibold whitespace-nowrap">
                        Owner
                      </th>
                      <th className="py-3 px-2 text-left text-slate-900 font-semibold whitespace-nowrap">
                        TX
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-[#F6F6F6]">
                    {trades.map((trade) => {
                      const type = getTradeTypeInfo(trade.action);
                      return (
                        <tr
                          key={`${trade.id}-${trade.timestamp}`}
                          className="hover:bg-[#E6EBEC] border-b border-b-[#1E1E1E] cursor-pointer"
                        >
                          {/* Time Column - Compact on all screens */}
                          <td className="py-3 px-2 text-slate-800 whitespace-nowrap text-xs sm:text-base w-[75px] sm:w-auto">
                            <div className="truncate">
                              {/* For small screens */}
                              <span className="block sm:hidden">
                                {formatCompactTime(new Date(trade.timestamp))}
                              </span>
                              {/* For larger screens */}
                              <span className="hidden sm:block">
                                {formatDistanceToNow(
                                  new Date(trade.timestamp),
                                  { addSuffix: true }
                                )}
                              </span>
                            </div>
                          </td>

                          <td className="py-3 px-2">
                            <span
                              className={`px-1.5 py-0.5 rounded whitespace-nowrap text-xs sm:text-base ${type.bg} ${type.textColor}`}
                            >
                              {type.text}
                            </span>
                          </td>
                          <td className="py-3 px-2 text-slate-800 whitespace-nowrap text-xs sm:text-base">
                            {trade.amount.toLocaleString()}
                          </td>
                          <td className="py-3 px-2 text-slate-800 whitespace-nowrap text-xs sm:text-base">
                            {prettyPrice(trade.price_usd)}
                          </td>
                          <td className="py-3 px-2 text-slate-800 whitespace-nowrap text-xs sm:text-base">
                            ${trade.total_usd.toFixed(2)}
                          </td>
                          <td className="py-3 px-2 text-xs sm:text-base">
                            <span
                              onClick={(e) => {
                                e.stopPropagation();
                                navigate(
                                  `/portfolio?address=${trade.created_by}`
                                );
                              }}
                              className="text-blue-600 hover:underline cursor-pointer text-xs sm:text-base"
                            >
                              {trade.created_by.slice(0, 3)}...
                              {trade.created_by.slice(-3)}
                            </span>
                          </td>
                          <td className="py-3 px-2">
                            {trade.operation_id ? (
                              <a
                                href={
                                  network?.toUpperCase() === "BUILDNET"
                                    ? `https://www.massexplo.com/tx/${trade.operation_id}?network=buildnet`
                                    : `https://explorer.massa.net/mainnet/operation/${trade.operation_id}`
                                }
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline text-xs sm:text-base"
                              >
                                {trade.operation_id.slice(0, 3)}...
                                {trade.operation_id.slice(-3)}
                              </a>
                            ) : (
                              <span className="text-gray-500 text-xs sm:text-base">
                                N/A
                              </span>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {trades.length > 0 && (
                <div className="flex justify-center items-center gap-4 mt-6">
                  <button
                    onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1.5 rounded-md border-2 border-[#1E1E1E] bg-white hover:bg-[#E6EBEC] disabled:opacity-50 text-xs"
                  >
                    Previous
                  </button>
                  <span className="text-slate-800 text-xs">
                    Page {currentPage}
                  </span>
                  <button
                    onClick={() => setCurrentPage((p) => p + 1)}
                    disabled={!hasMore}
                    className="px-3 py-1.5 rounded-md border-2 border-[#1E1E1E] bg-white hover:bg-[#E6EBEC] disabled:opacity-50 text-xs"
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          </>
        )}

        {/* --------------------- INFO TAB -------------------- */}
        {activeTab === "info" && tokenData && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5">
              <DetailCard title="About" className="text-gray-600">
                {tokenData.description}
              </DetailCard>

              <DetailCard title="Links">
                <div className="flex gap-4">
                  {tokenData.website && (
                    <a
                      href={tokenData.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-2 bg-[#f9fafb] rounded-lg hover:bg-[#e3e6f1] transition-colors"
                    >
                      <FaGlobe className="text-gray-400 w-6 h-6" />
                    </a>
                  )}
                </div>
              </DetailCard>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <DetailCard title="Token Details">
                <div className="flex flex-col gap-6">
                  <InfoItem
                    label="Supply:"
                    value={formatTokenAmount(
                      tokenData.total_supply ?? 0,
                      tokenData.symbol
                    )}
                  />
                  <InfoItem
                    label="Circulating Supply:"
                    value={
                      <>
                        {(
                          ((tokenData.circulated_supply ?? 0) /
                            (tokenData.total_supply ?? 1)) *
                          100
                        ).toFixed(1)}
                        %{" "}
                        <span className="text-gray-500">
                          (
                          {formatTokenAmount(
                            tokenData.circulated_supply ?? 0,
                            tokenData.symbol
                          )}
                          )
                        </span>
                      </>
                    }
                  />
                  <InfoItem
                    label="Launch date:"
                    value={
                      tokenData.created_at
                        ? new Date(tokenData.created_at).toLocaleDateString(
                            "en-US",
                            { year: "numeric", month: "long", day: "numeric" }
                          )
                        : "-"
                    }
                  />
                  <InfoItem
                    label="Description:"
                    value={tokenData.description}
                  />
                  <InfoItem
                    label="Burnable:"
                    value={tokenData.is_burnable ? "Yes" : "No"}
                  />
                  <InfoItem
                    label="Mintable:"
                    value={tokenData.is_mintable ? "Yes" : "No"}
                  />
                  <InfoItem
                    label="Freezable:"
                    value={tokenData.is_pausable ? "Yes" : "No"}
                  />
                </div>
              </DetailCard>
            </div>
          </>
        )}

        {/* --------------------- POOLS TAB -------------------- */}
        {activeTab === "pools" && (
          <>
            <div className="rounded-lg overflow-hidden mb-6">
              <div className="overflow-x-auto rounded-xl border-2 border-[#000] border-b-[6px] border-r-[6px]">
                <table className="w-full table-auto text-sm bg-[#F6F6F6]">
                  <thead className="bg-[#E6EBEC]">
                    <tr>
                      {["Pool", "Fee", "Liquidity", "Volume 24h", "APR"].map(
                        (h) => (
                          <th
                            key={h}
                            className="py-3 px-4 text-left text-slate-700 font-semibold"
                          >
                            {h}
                          </th>
                        )
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {pools.map((pool) => (
                      <tr
                        key={pool.id}
                        className="hover:bg-[#E6EBEC] border-b border-b-[#2B2C3B] cursor-pointer"
                        onClick={() => handlePoolClick(pool)}
                      >
                        <td className="py-4 px-6">
                          <div className="flex items-center gap-3">
                            <div className="flex -space-x-2">
                              {[pool.token0, pool.token1].map((t, i) => (
                                <div key={i}>
                                  {t.logo ? (
                                    <img
                                      src={t.logo}
                                      alt={t.symbol}
                                      className="w-7 h-7 rounded-full border-2 border-white"
                                    />
                                  ) : (
                                    <div className="w-7 h-7 flex items-center justify-center rounded-full bg-slate-300 text-white text-xs font-semibold">
                                      {t.symbol.slice(0, 2)}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                            <span className="font-medium text-slate-700">
                              {pool.token0.symbol}/{pool.token1.symbol}
                            </span>
                          </div>
                        </td>
                        <td className="py-4 px-6 text-slate-700">
                          {pool.feeTier}
                        </td>
                        <td className="py-4 px-6 text-slate-700">
                          {pool.liquidity}
                        </td>
                        <td className="py-4 px-6 text-slate-700">
                          {pool.volume24h}
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center gap-1 text-green-500">
                            {UpArrowIcon}
                            {pool.APR}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {isModalOpen && selectedPool && (
              <LiquidityModal
                pool={selectedPool}
                onClose={() => {
                  setIsModalOpen(false);
                  setSelectedPool(null);
                }}
                isConnected={!!connectedAccount}
                initialMode={initialMode}
              />
            )}
          </>
        )}
      </div>

      {/* ------------- right sidebar ------------- */}
      <div className="hidden xl:block col-start-10 2xl:col-start-10 col-span-6  sidebar-right-container">
        <SwapWidget defaultToTokenAddress={tokenData?.address} />
      </div>
    </div>
  );
};

/* ---------- reusable pieces ---------- */
const StatCard = ({
  label,
  value,
  secondary,
  isPositive,
}: {
  label: string;
  value: string | React.ReactNode;
  secondary?: string;
  isPositive?: boolean;
}) => (
  <div className="rounded-md bg-[#F9FAFB] border-2 border-b-4 border-[#1E1E1E] shadow-sm p-4">
    <p className="text-xs sm:text-sm text-gray-500 mb-1 sm:mb-2">{label}</p>
    <div className="text-lg sm:text-xl font-semibold text-slate-700">
      {value}
    </div>
    {secondary && (
      <div className="flex items-center gap-1 text-xs sm:text-sm">
        {typeof isPositive === "boolean" &&
          (isPositive ? UpArrowIcon : DownArrowIcon)}
        <span className={isPositive ? "text-green-500" : "text-red-500"}>
          {secondary}
        </span>
      </div>
    )}
  </div>
);

const DetailCard = ({
  title,
  children,
  className,
}: {
  title: string;
  children: React.ReactNode;
  className?: string;
}) => (
  <div className="border-2 border-[#1E1E1E] rounded-xl p-4 bg-white">
    <h3 className="text-lg font-bold text-slate-950 mb-3">{title}</h3>
    <div className={className}>{children}</div>
  </div>
);

const InfoItem = ({
  label,
  value,
}: {
  label: string;
  value: React.ReactNode;
}) => (
  <div className="flex flex-col gap-1">
    <p className="text-sm text-gray-500">{label}</p>
    <p className="text-lg font-semibold text-slate-700">{value}</p>
  </div>
);

export default TokenStats;
