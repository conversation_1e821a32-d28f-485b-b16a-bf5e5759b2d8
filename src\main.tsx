// import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";
import "@massalabs/react-ui-kit/src/global.css";
import { BrowserRouter } from "react-router-dom";
import { Provider } from "react-redux";
import "react-toastify/dist/ReactToastify.css";
import { ToastContainer } from "react-toastify";
import { store } from "./redux/store.ts";
import ReactGA from "react-ga4";
import { HelmetProvider } from "react-helmet-async";
import { MaintenanceProvider } from "./context/MaintenanceContext.tsx";

ReactGA.initialize("G-4M3SYP2BVS");
ReactGA.send({ hitType: "pageview", page: window.location.pathname });

createRoot(document.getElementById("root")!).render(
  // <StrictMode>

  <Provider store={store}>
    <BrowserRouter>
      <MaintenanceProvider>
        <HelmetProvider>
          <ToastContainer />
          <App />
        </HelmetProvider>
      </MaintenanceProvider>
    </BrowserRouter>
  </Provider>

  // </StrictMode>
);
