import { useState } from "react";
import { FaRegCopy } from "react-icons/fa";
import { shortenAddress } from "../lib/utils";

interface AddressWithCopyProps {
  address: string;
  shortenValue?: number;
  iconClassName?: string;
}

const AddressWithCopy: React.FC<AddressWithCopyProps> = ({
  address,
  shortenValue,
  iconClassName = "w-5 h-5",
}) => {
  const [isCopied, setIsCopied] = useState(false);

  const copyToClipboard = async (e: { stopPropagation: () => void }) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(address);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-slate-700">
        {shortenAddress(address, shortenValue)}
      </span>
      <button
        onClick={copyToClipboard}
        disabled={isCopied}
        className="text-gray-400 hover:text-blue-600 transition-colors disabled:opacity-50"
        title={isCopied ? "Copied!" : "Copy address"}
        aria-label={isCopied ? "Address copied" : "Copy address to clipboard"}
      >
        {isCopied ? (
          <span className="text-green-600">Copied!</span>
        ) : (
          <FaRegCopy className={iconClassName} />
        )}
      </button>
    </div>
  );
};

export default AddressWithCopy;
