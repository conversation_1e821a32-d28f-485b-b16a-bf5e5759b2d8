import { useEffect, useRef, useState } from "react";
import { useLocation, Link } from "react-router-dom";
import { useAccountStore } from "@massalabs/react-ui-kit";
import ConnectWalletModal from "./ConnectWalletModal";
import { shortenAddress } from "../lib/utils";
import Marquee from "react-fast-marquee";
import { useNavigate } from "react-router-dom";
// import { navItems } from "../lib/data";
import axiosInstance from "../lib/axios/axiosInstance";
import {
  FiSend,
  FiShoppingCart,
  FiRefreshCcw,
  // FiActivity,
  // FiInfo,
  // FiAlertTriangle,
} from "react-icons/fi";
import { IoMdSwap } from "react-icons/io";
import { GiToken } from "react-icons/gi";
import { PiTerminalWindow } from "react-icons/pi";
import { FaSwimmingPool } from "react-icons/fa";
import TokenLogo from "./TokenLogo";
import { toast } from "react-toastify";
import { prettyPrice } from "../lib/utils2";

export const navItems = [
  {
    label: "💸 Trade",
    path: "/",
    subItems: [
      {
        label: "Swap",
        path: "/",
        icon: <IoMdSwap className="mr-2 text-lg" />,
      },
      {
        label: "Send",
        path: "/send",
        icon: <FiSend className="mr-2 text-lg" />,
      },
      {
        label: "Bridge",
        path: "/bridge",
        icon: <FiShoppingCart className="mr-2 text-lg" />,
      },
      // {
      //   label: "Bridge",
      //   path: "/bridge",
      //   icon: <FiRefreshCcw className="mr-2 text-lg" />,
      // },
    ],
  },
  {
    label: "💧 Liquidity",
    path: "/liquidity",
  },
  {
    label: "📊 Portfolio",
    path: "/portfolio",
  },
  {
    label: "⚒️ Create",
    subItems: [
      {
        label: "Create Token",
        path: "/create-token",
        icon: <GiToken className="mr-2 text-lg" />,
      },
      {
        label: "Create Pool",
        path: "/create-pool",
        icon: <FaSwimmingPool className="mr-2 text-lg" />,
      },
      {
        label: "Create Widget",
        path: "/swap-widget-creator",
        icon: <PiTerminalWindow className="mr-2 text-lg" />,
      },
    ],
  },
  {
    label: "🤖 EagleAI",
    path: "/eagle-assistant",
  },
  {
    label: "🥇 Leaderboard",
    path: "/leaderboard",
  },
  // {
  //   label: "📚 More",
  //   subItems: [
  //     {
  //       label: "Developers Docs",
  //       path: "/devdocs",
  //       icon: <PiTerminalWindow className="mr-2 text-lg" />,
  //     },
  //     {
  //       label: "Network Status",
  //       path: "/status",
  //       icon: <FiActivity className="mr-2 text-lg" />,
  //       soon: true,
  //     },
  //     {
  //       label: "About Us",
  //       path: "/about",
  //       icon: <FiInfo className="mr-2 text-lg" />,
  //     },
  //     {
  //       label: "Disclaimer",
  //       path: "/disclaimer",
  //       icon: <FiAlertTriangle className="mr-2 text-lg" />,
  //     },
  //   ],
  // },
];

const Header = () => {
  const [toggleConnectWalletModal, setToggleConnectWalletModal] =
    useState(false);
  const [massaChange24h, setMassaChange24h] = useState<number | null>(null);
  const { connectedAccount, currentWallet } = useAccountStore();
  const [network, setNetwork] = useState<string | null>(null);
  const location = useLocation();
  const isActive = (path: string) => location.pathname === path;
  const hasActiveChild = (item: any) =>
    item.subItems?.some((sub: any) => location.pathname === sub.path);
  const [selectedAccount, setSelectedAccount] = useState(
    connectedAccount?.address || ""
  );
  const [massaPrice, setMassaPrice] = useState<string | null>(null);
  const navigate = useNavigate();

  // Mobile nav states
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const mobileNavRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (mobileNavOpen) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }
  }, [mobileNavOpen]);

  // Close menu on escape key press
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") closeMobileNav();
    };
    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, []);
  // Track open/closed submenus by parent label
  const [openMenus, setOpenMenus] = useState<{ [key: string]: boolean }>({});

  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      const fetchTokens = async () => {
        try {
          const response = await axiosInstance.get(
            `/tokens/search?query=${searchQuery}`
          );
          setSearchResults(response.data);
        } catch (error) {
          console.error("Error fetching tokens:", error);
          setSearchResults([]);
        }
      };
      fetchTokens();
    }, 300);

    return () => clearTimeout(delayDebounceFn);
  }, [searchQuery]);
  // Handle clicks outside the search container
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setIsSearchFocused(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleMenu = (label: string) => {
    setOpenMenus((prev) => ({ ...prev, [label]: !prev[label] }));
  };

  const closeMobileNav = () => {
    setMobileNavOpen(false);
    setOpenMenus({});
  };

  // const fetchMassaPrice = async () => {
  //   try {
  //     const response = await fetch(
  //       "https://api.bitget.com/api/v2/spot/market/tickers?symbol=MASUSDT"
  //     );
  //     const data = await response.json();
  //     if (data.data[0].lastPr && data.data[0].change24h) {
  //       const priceNum = parseFloat(data.data[0].lastPr);
  //       const priceFormatted = priceNum.toFixed(4);
  //       setMassaPrice(priceFormatted);
  //       setMassaChange24h(parseFloat(data.data[0].change24h) * 100);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching Massa price:", error);
  //   }
  // };

  const fetchMassaPrice = async () => {
    try {
      const { data } = await axiosInstance.get("/global/massa");
      setMassaPrice(data.current_price.toFixed(4));
      setMassaChange24h(data.change_percentage_24h);
    } catch (err) {
      console.error("Error fetching Massa price:", err);
    }
  };

  useEffect(() => {
    fetchMassaPrice();
    const intervalTime =
      Number(import.meta.env.VITE_PRICE_MAS_FETCH_INTERVAL) || 60000;
    const interval = setInterval(fetchMassaPrice, intervalTime);
    return () => clearInterval(interval);
  }, []);
  useEffect(() => {
    const fetchWalletInfo = async () => {
      if (connectedAccount) {
        const networkInfo = await currentWallet?.networkInfos();
        const networkName = networkInfo?.name ?? null;
        setNetwork(networkName);

        console.log("networkName", networkName);

        let newtorkFromEnv = import.meta.env.VITE_AI_NETWORK || "BUILDNET";

        if (networkName?.toUpperCase() !== newtorkFromEnv.toUpperCase()) {
          toast.warning(
            `Wrong wallet network. Please, switch to ${newtorkFromEnv.toUpperCase()} in your ${currentWallet?.name()} wallet settings`,
            {
              position: "top-center",
              autoClose: false,
              hideProgressBar: false,
              closeOnClick: true,
              pauseOnHover: true,
              draggable: true,
              toastId: "buildnet-warning",
            }
          );
        }
      } else {
        setNetwork(null);
      }
    };

    fetchWalletInfo();
    const handleNetworkChange = async () => {
      await fetchWalletInfo();
    };
    currentWallet?.listenNetworkChanges(handleNetworkChange);
  }, [
    toggleConnectWalletModal,
    connectedAccount,
    currentWallet?.networkInfos?.name,
  ]);

  useEffect(() => {
    const openParentsForActiveChild = () => {
      navItems.forEach((item) => {
        // If the item has an active child route, keep it open
        if (hasActiveChild(item)) {
          setOpenMenus((prev) => ({ ...prev, [item.label]: true }));
        }
      });
    };

    openParentsForActiveChild();
  }, [location]);

  useEffect(() => {
    setSelectedAccount(connectedAccount?.address || "");
  }, [connectedAccount, connectedAccount?.address]);

  useEffect(() => {
    console.log("search result", searchResults);
  }, [searchResults]);
  return (
    <>
      <ConnectWalletModal
        toggleConnectWalletModal={toggleConnectWalletModal}
        setToggleConnectWalletModal={setToggleConnectWalletModal}
      />

      <header className="backdrop-blur-2xl sticky top-0 z-[55] w-full bg-transparent text-black">
        <div className="flex justify-between items-center py-3 px-6 gap-3 relative">
          <div className="flex items-center gap-4">
            <Link to="/" className="flex items-center gap-2">
              <img
                src="/images/logo header.svg"
                alt="EagleFi logo"
                className="w-[45px] h-[45px]"
              />
              <span className="text-lg font-bold hidden sm:flex items-center  ">
                EagleFi
                {/* <span className="text-sm font-normal mt-auto h-full ml-1 text-amber-400">
                  βeta
                </span> */}
              </span>
            </Link>
          </div>
          <nav className="hidden xl:flex gap-6 items-center font-medium">
            <div className="dropdown">
              <button
                className={`dropbtn hover:bg-white/10 rounded-xl font-bold  py-2 2xl:px-4 px-2 transition-colors duration-200 ease-in ${
                  isActive("/") ? "bg-white/20" : "hover:bg-white/10"
                }`}
                onClick={() => navigate("/")}
              >
                💸 Trade
              </button>
              <div className="dropdown-content rounded-md border-2 border-[#1E1E1E]">
                <div className="bg-white rounded-md text-white ">
                  <Link
                    to="/"
                    className={` transition-colors duration-200 border-b border-[#1E1E1E]  ease-in py-2 px-4 block ${
                      isActive("/")
                        ? "text-[#1E1E1E] font-extrabold"
                        : "hover:bg-gray-100 text-[#1E1E1E] font-medium"
                    }`}
                  >
                    <div className="flex items-center">
                      <IoMdSwap className="mr-2 text-current" />
                      Swap
                    </div>
                  </Link>

                  <Link
                    to="/send"
                    className={` transition-colors duration-200 border-b border-[#1E1E1E]  ease-in py-2 px-4 block ${
                      isActive("/send")
                        ? "text-[#1E1E1E] font-extrabold"
                        : "hover:bg-gray-100 text-[#1E1E1E] font-medium"
                    }`}
                  >
                    <div className="flex items-center">
                      <FiSend className="mr-2 text-current" />
                      Send
                    </div>
                  </Link>
                  <Link
                    to="/bridge"
                    className={` transition-colors duration-200 border-b border-[#1E1E1E]  ease-in py-2 px-4 block ${
                      isActive("/bridge")
                        ? "text-[#1E1E1E] font-extrabold"
                        : "hover:bg-gray-100 text-[#1E1E1E] font-medium"
                    }`}
                  >
                    <div className="flex items-center">
                      {/* <FiShoppingCart className="mr-2 text-current" />
                      Buy Crypto */}
                      <FiRefreshCcw className="mr-2 text-current" />
                      Bridge
                    </div>
                  </Link>
                  {/* <Link
                    to="/bridge"
                    className={` transition-colors duration-200 border-b border-[#1E1E1E]  ease-in py-2 px-4 block ${
                      isActive("/bridge")
                        ? "text-[#1E1E1E] font-extrabold"
                        : "hover:bg-gray-100 text-[#1E1E1E] font-medium"
                    }`}
                  >
                    <div className="flex items-center">
                      <FiRefreshCcw className="mr-2 text-current" />
                      Bridge
                    </div>
                  </Link> */}
                </div>
              </div>
            </div>
            <Link
              to="/liquidity"
              className={`py-2 px-2 rounded-lg transition duration-200 ease-in ${
                isActive("/liquidity") ? "bg-white/20" : "hover:bg-white/10"
              }`}
            >
              💧 Liquidity
            </Link>
            <Link
              to="/portfolio"
              className={`py-2 px-2 rounded-lg transition duration-200 ease-in ${
                isActive("/portfolio") ? "bg-white/20" : "hover:bg-white/10"
              }`}
            >
              📊 Portfolio
            </Link>

            <div className="dropdown">
              <button className="dropbtn hover:bg-white/10 rounded-xl font-bold  py-2 2xl:px-4 transition-colors duration-200 ease-in">
                ⚒️ Create
              </button>
              <div
                className="dropdown-content rounded-md border-2 border-[#1E1E1E] "
                style={{ minWidth: "220px" }}
              >
                <div className="bg-white rounded-md text-[#1E1E1E] ">
                  <Link
                    to="/create-token"
                    className={` transition-colors duration-200 border-b border-[#1E1E1E]  ease-in py-2 px-4 block ${
                      isActive("/create-token")
                        ? "text-[#1E1E1E] font-extrabold"
                        : "hover:bg-gray-100 text-[#1E1E1E] font-medium"
                    }`}
                  >
                    <div className="flex items-center">
                      <GiToken className="mr-2 text-current" />
                      Create token
                    </div>
                  </Link>
                  <Link
                    to="/create-pool"
                    className={` transition-colors duration-200 border-b border-[#1E1E1E]  ease-in py-2 px-4 block ${
                      isActive("/create-pool")
                        ? "text-[#1E1E1E] font-extrabold"
                        : "hover:bg-gray-100 text-[#1E1E1E] font-medium"
                    }`}
                  >
                    <div className="flex items-center">
                      <FaSwimmingPool className="mr-2 text-current" />
                      Create Pool
                    </div>
                  </Link>
                  <Link
                    to="/swap-widget-creator"
                    className={` transition-colors duration-200 border-b border-[#1E1E1E]  ease-in py-2 px-4 block ${
                      isActive("/swap-widget-creator")
                        ? "text-[#1E1E1E] font-extrabold"
                        : "hover:bg-gray-100 text-[#1E1E1E] font-medium"
                    }`}
                  >
                    <div className="flex items-center">
                      <PiTerminalWindow className="mr-2 text-current" />
                      Create Swap Widget
                    </div>
                  </Link>
                </div>
              </div>
            </div>

            <Link
              to="/eagle-assistant"
              className={`py-2 px-2 rounded-lg transition duration-200 ease-in ${
                isActive("/eagle-assistant")
                  ? "bg-white/20"
                  : "hover:bg-white/10"
              }`}
            >
              🤖 EagleAI
            </Link>
            <Link
              to="/leaderboard"
              className={`py-2 px-2 rounded-lg transition duration-200 ease-in ${
                isActive("/leaderboard") ? "bg-white/20" : "hover:bg-white/10"
              }`}
            >
              🥇 Leaderboard
            </Link>
          </nav>
          <div className="flex items-center gap-4 flex-1 justify-end max-w-[650px]">
            {/* <div className="flex items-center gap-2 max-sm:hidden">
              <MassaLogo />
              <span className="text-md flex items-center"> */}
            {/* {massaPrice ? `$${massaPrice}  ` : "Loading..."} */}
            {/* $0.0654 */}
            {/* {massaChange24h !== null && (
                  <span
                    className={`ml-2 text-sm flex items-center ${
                      massaChange24h > 0 ? "text-green-500" : "text-red-500"
                    }`}
                  >
                    {massaChange24h > 0 ? "▲ " : "▼ "}
                    {Math.abs(massaChange24h).toFixed(2)}%
                  </span>
                )} */}
            {/* </span>
            </div> */}
            {/* <div className="dropdown max-sm:hidden">
              <button className="dropbtn text-white hover:bg-white/10 rounded-xl flex items-center gap-2 font-bold  py-2 2xl:px-2 px-2 transition-colors duration-200 ease-in">
                <img
                  src="/icons/globe-icon.svg"
                  alt="Language"
                  className="w-6 h-6"
                />
               
              </button>
              <div
                className="dropdown-content rounded-xl border border-[#e3e6f1] "
                style={{ right: "-9px" }}
              >
                <div className="bg-white rounded-xl text-black">
                  <Link
                    to="/"
                    className={`rounded-lg transition-colors duration-200 ease-in py-2 px-4 block
                      
                        "hover:bg-gray-100"
                    `}
                  >
                    English
                  </Link>
                  <Link
                    to="/"
                    className={`rounded-lg transition-colors duration-200 ease-in py-2 px-4 block
                      
                      "hover:bg-gray-100"
                  `}
                  >
                    Français
                  </Link>
                  <Link
                    to="/"
                    className={`rounded-lg transition-colors duration-200 ease-in py-2 px-4 block
                      
                      "hover:bg-gray-100"
                  `}
                  >
                    العربية
                  </Link>
                </div>
              </div>
            </div> */}
            <div
              className="flex-1 flex flex-col md:flex-row md:items-center gap-4 relative w-full max-w-[400px] mx-auto max-xl:hidden"
              ref={searchRef}
            >
              <div className="flex items-center rounded-md  px-3 py-2.5 border-2 border-[#000000] bg-white relative w-full">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  data-prefix="fas"
                  data-icon="magnifying-glass"
                  className="w-4 h-4 text-gray-500 mr-2"
                  role="img"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 512 512"
                >
                  <path
                    fill="currentColor"
                    d="M416 208c0 45.9-14.9 88.3-40 
                     122.7L502.6 457.4c12.5 12.5 
                     12.5 32.8 0 45.3s-32.8 12.5-45.3 
                     0L330.7 376c-34.4 25.2-76.8 
                     40-122.7 40C93.1 416 0 
                     322.9 0 208S93.1 0 208 
                     0s208 93.1 208 208zM208 
                     352a144 144 0 1 0 0-288 
                     144 144 0 1 0 0 288z"
                  />
                </svg>
                <input
                  type="text"
                  placeholder="Search tokens..."
                  className="bg-transparent w-full text-sm text-slate-700 placeholder:text-gray-500 focus:outline-none"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setIsSearchFocused(true)}
                />

                {/* Dropdown Suggestions */}
                {isSearchFocused && (
                  <div className="absolute top-full left-0 right-0 mt-2 bg-white border-2 border-black rounded-md z-50 max-h-80 overflow-y-auto">
                    {searchResults.length > 0 ? (
                      searchResults.map((token) => (
                        <Link
                          key={token.address}
                          to={`/token/${token.address}`}
                          className="flex items-center p-3 hover:bg-blue-50 transition-colors border-b border-black"
                          onClick={() => {
                            setSearchQuery("");
                            setIsSearchFocused(false);
                          }}
                        >
                          {token.logo ? (
                            <TokenLogo
                              token={token}
                              className="w-7 h-7 mr-3"
                              width="24"
                              height="24"
                            />
                          ) : (
                            // <img
                            //   src={token.logo}
                            //   alt={token.symbol}
                            //   className="w-6 h-6 rounded-full mr-3"
                            // />
                            <div className="w-6 h-6 mr-3 rounded-full bg-slate-300 text-white text-xs font-semibold flex items-center justify-center">
                              {token.symbol.slice(0, 3)}
                            </div>
                          )}
                          <div className="flex-1">
                            <div className="flex justify-between items-center">
                              <span className="font-medium text-gray-900">
                                {token.symbol}
                              </span>
                              <span className="text-sm text-gray-600">
                                {prettyPrice(token.mas_price)} MAS
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-500">
                                {token.name}
                              </span>
                              <small
                                className={`  text-xs opacity-75  flex items-center`}
                              >
                                {shortenAddress(token.address, 4)}
                              </small>
                            </div>
                          </div>
                        </Link>
                      ))
                    ) : (
                      <div className="p-3 text-gray-500 text-sm">
                        No tokens found
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
            <div className="flex">
              {connectedAccount ? (
                <div className="hidden lg:inline-flex  px-2 text-xs rounded-lg font-medium items-center gap-1 text-black">
                  <span
                    className={`rounded-full w-2 h-2 inline-block cursor-help animate-pulse ${
                      network?.toUpperCase() === "BUILDNET"
                        ? "bg-[#F6C955]"
                        : "bg-[#31d0aa]"
                    }`}
                    title={network?.toUpperCase()}
                  ></span>{" "}
                  {/* <span> {network?.toUpperCase()} </span> */}
                </div>
              ) : (
                <>
                  <div className="hidden lg:inline-flex  px-2 text-xs  rounded-lg font-medium items-center gap-1 text-black">
                    <span
                      className="bg-red-500 animate-pulse rounded-full cursor-help  w-2 h-2 inline-block"
                      title="OFFLINE"
                    ></span>{" "}
                    {/* <span>OFFLINE</span> */}
                  </div>
                </>
              )}
              <button
                onClick={() => setToggleConnectWalletModal(true)}
                className="bg-[#F6C955] border-b-[6px] border-2 border-[#000000] text-[#333333] font-medium px-4 py-2 rounded-lg hover:bg-[#FDE68A] transition-all duration-200 ease-in-out"
              >
                {connectedAccount
                  ? shortenAddress(selectedAccount, 4)
                  : "Connect Wallet"}
              </button>
            </div>

            {/* Language Dropdown */}

            <button
              className="xl:hidden focus:outline-none"
              onClick={() => setMobileNavOpen(!mobileNavOpen)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-7 w-7"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
        <div className="bg-[#FDC500] py-2 relative border border-t-2 border-b-2 border-[#000000]">
          <Marquee gradient={false} speed={50} pauseOnHover={true}>
            <div className="flex gap-60">
              <span className="mx-4 text-black font-medium text-md">
                Incentive program is live!!! 🤑
              </span>
              <span className="mx-4 text-black font-medium text-md">
                EagleFi gives you wings. 💸
              </span>
              <span className="mx-4 text-black font-medium text-md">
                Incentive program is live!!! 🤑
              </span>
              <span className="mx-4 text-black font-medium text-md">
                EagleFi gives you wings. 💸
              </span>
            </div>
          </Marquee>
          <div className="absolute z-30 right-0 top-1/2 transform -translate-y-1/2 bg-[#FDC500] h-full w-fit flex items-center justify-center">
            <div className=" bg-[#fff] border-2 border-[#000000] text-[#333333] font-bold px-4 py-1 rounded-[6px] w-fit ">
              <div className="flex items-center gap-2">
                {/* <MassaLogo /> */}
                <img
                  src="/images/massa.jpg"
                  alt="Massa logo"
                  className="w-6 h-6 rounded-full"
                />
                <span className="text-md flex items-center">
                  {massaPrice ? `$${massaPrice}  ` : "Loading..."}
                  {/* $0.0654 */}
                  {massaChange24h !== null && (
                    <span
                      className={`ml-2 text-sm flex items-center w-max ${
                        massaChange24h > 0 ? "text-green-500" : "text-red-500"
                      }`}
                    >
                      {massaChange24h > 0 ? "▲ " : "▼ "}
                      {Math.abs(massaChange24h).toFixed(2)}%
                    </span>
                  )}
                </span>
              </div>
            </div>
          </div>
          <div className="absolute z-30 left-0 top-1/2 transform -translate-y-1/2 bg-[#FDC500] h-full w-[80px] flex items-center justify-center">
            <div className=" bg-[#fff] border-2 border-[#000000] text-[#333333] font-bold px-4 py-1 rounded-lg w-fit ">
              ADS
            </div>
          </div>
        </div>
        {/* -- MOBILE SIDEBAR NAV & OVERLAY -- */}
        {mobileNavOpen && (
          <>
            {/* Overlay with fade animation */}
            <div
              className="fixed h-[100vh] inset-0  z-[9999] bg-black/50 transition-opacity duration-300 opacity-0 animate-fade-in"
              onClick={closeMobileNav}
            ></div>

            {/* Side Panel with smooth slide-in */}
            <div
              ref={mobileNavRef}
              className={`
    fixed top-0 right-0 z-[9999]
    w-[85%] sm:w-[300px] h-screen 
    bg-white border-l-4 border-black
    flex flex-col
    overflow-y-auto // Ensure content can scroll
    transform-gpu
    transition-transform
    duration-300
    ease-[cubic-bezier(0.4,0,0.2,1)]
    ${mobileNavOpen ? "translate-x-0" : "translate-x-full"}
    shadow-2xl
  `}
            >
              {/* Close Button with better visibility */}
              <div className="flex justify-between items-center p-4 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">Menu</h2>
                <button
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  onClick={closeMobileNav}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-gray-700"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth={2}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              {/* Menu Items with improved interactions */}
              <div className="px-4 pb-8 pt-4">
                {navItems.map((item) => {
                  const hasSubItems = item.subItems && item.subItems.length > 0;
                  const parentIsActive =
                    isActive(item.path ?? "") || hasActiveChild(item);
                  const isExpanded = openMenus[item.label] || false;

                  return (
                    <div key={item.label} className="mb-1">
                      {!hasSubItems ? (
                        <Link
                          to={item.path ?? "#"}
                          className={`flex items-center py-3 px-3 rounded-lg transition-colors
                      ${
                        parentIsActive
                          ? "bg-yellow-100 text-black font-semibold"
                          : "hover:bg-gray-50 text-gray-700"
                      }
                    `}
                          onClick={closeMobileNav}
                        >
                          {item.label}
                        </Link>
                      ) : (
                        <>
                          <button
                            onClick={() => toggleMenu(item.label)}
                            className={`w-full flex items-center justify-between py-3 px-3 rounded-lg
                        transition-colors ${
                          isExpanded ? "bg-gray-50" : "hover:bg-gray-50"
                        }
                      `}
                          >
                            <span
                              className={parentIsActive ? "font-semibold" : ""}
                            >
                              {item.label}
                            </span>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className={`h-5 w-5 transform transition-transform duration-200
                          ${isExpanded ? "rotate-180" : "rotate-0"}
                        `}
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </button>

                          {/* Sub-items with smooth animation */}
                          <div
                            className={`grid transition-all duration-300 ease-out overflow-hidden
                        ${isExpanded ? "grid-rows-[1fr]" : "grid-rows-[0fr]"}
                      `}
                          >
                            <div className="min-h-0">
                              <div className="ml-4 pl-2 border-l-2 border-gray-200">
                                {item.subItems?.map((sub) => (
                                  <Link
                                    key={sub.path}
                                    to={sub.path}
                                    className={`flex items-center py-2.5 px-3 rounded-lg
                                transition-opacity duration-200
                                ${
                                  isActive(sub.path)
                                    ? "bg-yellow-50 text-black font-medium"
                                    : "hover:bg-gray-50 text-gray-600"
                                }
                              `}
                                    onClick={closeMobileNav}
                                  >
                                    {sub.icon}
                                    {sub.label}
                                  </Link>
                                ))}
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        )}
      </header>
    </>
  );
};

export default Header;
