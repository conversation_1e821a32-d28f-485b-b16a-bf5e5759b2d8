import React, { useEffect, useState } from "react";
import axiosInstance from "../lib/axios/axiosInstance";
import AddressWithCopy from "../components/AddressWithCopy";
import { formatLargeNumber } from "../components/TokensListTable";
// import { LP_DECIMALS } from "../lib/data";
import { formatUnits } from "@massalabs/massa-web3";
import TokenLogo from "../components/TokenLogo";
import { useNavigate } from "react-router-dom";

interface TradingLeaderboardEntry {
  trader: string;
  total_volume: {
    mas: number;
    usd: number;
  };
  points: number;
  trade_count: number;
  rank: number;
}

interface PoolToken {
  address: string;
  decimals: number;
  logo: string;
  name: string;
  symbol: string;
}

interface PoolLeaderboardEntry {
  rank: number;
  pool_address: string;
  a_token: PoolToken;
  b_token: PoolToken;
  a_reserve: number;
  b_reserve: number;
  total_lp_supply: string;
  total_liquidity_usd: number;
}

interface PoolUserLeaderboardEntry {
  rank: number;
  pool_address: string;
  user_address: string;
  lp_amount: string;
}

interface PaginatedResponse<T> {
  data?: T[];
  leaderboard?: T[];
  total_pages: number;
  total_items: number;
  current_page: number;
  per_page: number;
}

const useLeaderboard = <T,>(endpoint: string, limit: number) => {
  const [data, setData] = useState<T[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  useEffect(() => {
    const id = setTimeout(() => setDebouncedSearchTerm(searchTerm), 500);
    return () => clearTimeout(id);
  }, [searchTerm]);

  /* Reset page if search term changes*/
  useEffect(() => setCurrentPage(1), [debouncedSearchTerm]);

  useEffect(() => {
    if (!endpoint) return;

    const fetchData = async () => {
      setIsFetching(true);
      setError(null);
      try {
        const offset = (currentPage - 1) * limit;
        const params: Record<string, any> = { limit, offset };
        if (debouncedSearchTerm) {
          params.search = debouncedSearchTerm;
        }
        const res = await axiosInstance.get<PaginatedResponse<T>>(endpoint, {
          params,
        });

        const items: T[] =
          (res.data.data as T[]) ?? (res.data.leaderboard as T[]) ?? [];

        setData(items);

        const totalPages: number =
          typeof res.data.total_pages === "number"
            ? res.data.total_pages
            : Math.ceil((res.data.total_items ?? items.length) / limit);

        setHasMore(currentPage < totalPages);
      } catch (err) {
        console.error("Leaderboard fetch error:", err);
        setError("Failed to load leaderboard data");
      } finally {
        setIsFetching(false);
      }
    };

    fetchData();
  }, [debouncedSearchTerm, currentPage, endpoint, limit]);

  return {
    data,
    currentPage,
    setCurrentPage,
    hasMore,
    isFetching,
    error,
    searchTerm,
    setSearchTerm,
  };
};

const Leaderboard: React.FC = () => {
  const navigate = useNavigate();
  const limit = 10;
  const [activeTab, setActiveTab] = useState<"trading" | "liquidity">(
    "trading"
  );
  const [selectedPool, setSelectedPool] = useState<PoolLeaderboardEntry | null>(
    null
  );

  /* Data hooks ------------------------------------------------------------- */
  const tradingLeaderboard = useLeaderboard<TradingLeaderboardEntry>(
    "/users/leaderboard",
    limit
  );
  const liquidityPools = useLeaderboard<PoolLeaderboardEntry>(
    "/leaderboard/lp/pools",
    limit
  );
  const liquidityUsers = useLeaderboard<PoolUserLeaderboardEntry>(
    selectedPool
      ? `/leaderboard/lp/pools/${encodeURIComponent(
          selectedPool.pool_address
        )}/users`
      : "",
    limit
  );

  /* Helpers ---------------------------------------------------------------- */
  const getRankBadge = (rank: number) => {
    if (rank === 1) return "🥇";
    if (rank === 2) return "🥈";
    if (rank === 3) return "🥉";
    return <span className="text-gray-500">{rank}</span>;
  };

  const getPoolTokenPair = (pool: PoolLeaderboardEntry) => (
    <div className="flex items-center gap-2">
      <div className="flex -space-x-2">
        <TokenLogo token={pool.a_token} />
        <TokenLogo token={pool.b_token} />
      </div>
      <span className="font-medium">
        {pool.a_token.symbol}/{pool.b_token.symbol}
      </span>
    </div>
  );
  const handleAddressClick = (address: string, e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/portfolio?address=${address}`);
  };

  /* -------------------- Liquidity view (pools or users) ------------------- */
  const renderLiquidityContent = () => {
    /* ------------ Pool contributors (inside a selected pool) ------------- */
    if (selectedPool) {
      return (
        <div>
          <div
            className="flex items-center mb-4 cursor-pointer p-2 rounded w-fit"
            onClick={() => setSelectedPool(null)}
          >
            <svg
              className="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Back to Pools
          </div>

          {/* Pool header ---------------------------------------------------- */}
          <div className="bg-[#F9FAFB] rounded-xl p-4 border-2 border-[#1E1E1E] mb-4">
            <div className="flex items-center gap-4 flex-wrap">
              {getPoolTokenPair(selectedPool)}
              <div className="text-sm text-gray-600">
                Total Liquidity: $
                {formatLargeNumber(selectedPool.total_liquidity_usd)}
              </div>
            </div>
          </div>

          {/* Users table ---------------------------------------------------- */}
          <div className="overflow-x-auto rounded-xl border-2 border-[#000000] border-b-[6px] border-r-[6px]">
            <table className="w-full table-auto text-sm bg-[#F6F6F6]">
              <thead className="bg-[#E6EBEC]">
                <tr>
                  <th className="py-3 px-4 text-left">Rank</th>
                  <th className="py-3 px-4 text-left">User</th>
                  <th className="py-3 px-4 text-left">LP Amount</th>
                </tr>
              </thead>
              <tbody>
                {liquidityUsers.isFetching
                  ? /* Skeleton rows ----------------------------------------- */
                    Array.from({ length: limit }).map((_, i) => (
                      <tr key={i} className="animate-pulse">
                        <td className="py-3 px-4">
                          <div className="h-4 bg-gray-200 rounded"></div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="h-4 bg-gray-200 rounded"></div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="h-4 bg-gray-200 rounded"></div>
                        </td>
                      </tr>
                    ))
                  : /* Data rows --------------------------------------------- */
                    liquidityUsers.data.map((user) => (
                      <tr
                        key={user.user_address}
                        className="hover:bg-[#E6EBEC] border-b border-b-[#1E1E1E]"
                      >
                        <td className="py-3 px-4">{getRankBadge(user.rank)}</td>
                        <td className="py-3 px-4">
                          <div
                            onClick={(e) =>
                              handleAddressClick(user.user_address, e)
                            }
                            className="cursor-pointer hover:text-blue-600"
                          >
                            <AddressWithCopy address={user.user_address} />
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          {formatLargeNumber(
                            Number(
                              formatUnits(
                                BigInt(user.lp_amount),
                                Math.max(
                                  selectedPool.a_token.decimals,
                                  selectedPool.b_token.decimals
                                )
                              )
                            )
                          )}{" "}
                          LP
                        </td>
                      </tr>
                    ))}
              </tbody>
            </table>
          </div>
        </div>
      );
    }

    /* ------------------------ Pools leaderboard table --------------------- */
    return (
      <div className="overflow-x-auto rounded-xl border-2 border-[#000000] border-b-[6px] border-r-[6px]">
        <table className="w-full table-auto text-sm bg-[#F6F6F6]">
          <thead className="bg-[#E6EBEC]">
            <tr>
              <th className="py-3 px-4 text-left">Rank</th>
              <th className="py-3 px-4 text-left">Pool</th>
              <th className="py-3 px-4 text-left">Reserves</th>
              <th className="py-3 px-4 text-left">Total Liquidity</th>
            </tr>
          </thead>
          <tbody>
            {liquidityPools.isFetching
              ? Array.from({ length: limit }).map((_, i) => (
                  <tr key={i} className="animate-pulse">
                    {Array.from({ length: 4 }).map((__, j) => (
                      <td key={j} className="py-3 px-4">
                        <div className="h-4 bg-gray-200 rounded"></div>
                      </td>
                    ))}
                  </tr>
                ))
              : liquidityPools.data.map((pool) => (
                  <tr
                    key={pool.pool_address}
                    onClick={() => setSelectedPool(pool)}
                    className="hover:bg-[#E6EBEC] cursor-pointer border-b border-b-[#1E1E1E]"
                  >
                    <td className="py-3 px-4">{getRankBadge(pool.rank)}</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        {getPoolTokenPair(pool)}
                        <AddressWithCopy address={pool.pool_address} />
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex flex-col">
                        <span>
                          {formatLargeNumber(pool.a_reserve)}{" "}
                          {pool.a_token.symbol}
                        </span>
                        <span>
                          {formatLargeNumber(pool.b_reserve)}{" "}
                          {pool.b_token.symbol}
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      ${formatLargeNumber(pool.total_liquidity_usd)}
                    </td>
                  </tr>
                ))}
          </tbody>
        </table>
      </div>
    );
  };

  /* ----------------------------- Main render ------------------------------ */
  return (
    <div className="container mx-auto px-4 md:px-6 lg:px-8 my-8">
      <div className="bg-white border-2 border-[#1E1E1E] rounded-3xl p-6 max-sm:p-3">
        {/* ------------------------------ Tabs ----------------------------- */}
        <div className="flex mb-6 bg-[#1E1E1E] rounded-lg p-1">
          <button
            onClick={() => setActiveTab("trading")}
            className={`flex-1 px-6 py-3 font-bold text-sm rounded-md transition-all duration-200 ease-in-out ${
              activeTab === "trading"
                ? "bg-[#FFFFFF] text-[#1E1E1E]"
                : "text-[#FFFFFF] hover:bg-[#333333]"
            }`}
          >
            Trading Leaderboard
          </button>
          <button
            onClick={() => setActiveTab("liquidity")}
            className={`flex-1 px-6 py-3 font-bold text-sm rounded-md transition-all duration-200 ease-in-out ${
              activeTab === "liquidity"
                ? "bg-[#FFFFFF] text-[#1E1E1E]"
                : "text-[#FFFFFF] hover:bg-[#333333]"
            }`}
          >
            Liquidity Leaderboard
          </button>
        </div>

        {/* ----------------------- Header & search bar ---------------------- */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <div>
            <h2 className="text-3xl font-bold text-slate-950 mb-1">
              {activeTab === "trading"
                ? "Trading Leaderboard"
                : selectedPool
                ? "Pool Contributors"
                : "Liquidity Pools"}
            </h2>
            <span className="text-sm text-stone-800">
              {activeTab === "trading"
                ? "Top traders based on trading volume and activity"
                : selectedPool
                ? `Users contributing to ${selectedPool.a_token.symbol}/${selectedPool.b_token.symbol} pool`
                : "Top liquidity pools based on total liquidity"}
            </span>
          </div>

          {/* Search -------------------------------------------------------- */}
          <div className="flex items-center rounded-md px-3 py-2.5 border-2 border-[#1E1E1E] bg-white relative w-full max-w-[400px]">
            <svg className="w-4 h-4 text-gray-500 mr-2" viewBox="0 0 512 512">
              <path
                fill="currentColor"
                d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z"
              />
            </svg>
            <input
              type="text"
              placeholder={
                activeTab === "trading"
                  ? "Search address"
                  : selectedPool
                  ? "Search user address"
                  : "Search pool address"
              }
              value={
                activeTab === "trading"
                  ? tradingLeaderboard.searchTerm
                  : selectedPool
                  ? liquidityUsers.searchTerm
                  : liquidityPools.searchTerm
              }
              onChange={(e) => {
                if (activeTab === "trading") {
                  tradingLeaderboard.setSearchTerm(e.target.value);
                } else if (selectedPool) {
                  liquidityUsers.setSearchTerm(e.target.value);
                } else {
                  liquidityPools.setSearchTerm(e.target.value);
                }
              }}
              className="bg-transparent w-full text-sm text-slate-700 placeholder:text-gray-500 focus:outline-none"
            />
          </div>
        </div>

        {/* ----------------------------- Tables ---------------------------- */}
        <div className="hidden md:block">
          {activeTab === "trading" ? (
            /* Trading leaderboard ------------------------------------------- */
            <div className="overflow-x-auto rounded-xl border-2 border-[#000000] border-b-[6px] border-r-[6px]">
              <table className="w-full table-auto text-sm bg-[#F6F6F6]">
                <thead className="bg-[#E6EBEC]">
                  <tr>
                    <th className="py-3 px-4 text-left">Rank</th>
                    <th className="py-3 px-4 text-left">Trader</th>
                    <th className="py-3 px-4 text-left">Total Volume</th>
                    <th className="py-3 px-4 text-left">Trades</th>
                  </tr>
                </thead>
                <tbody>
                  {tradingLeaderboard.isFetching
                    ? Array.from({ length: limit }).map((_, i) => (
                        <tr key={i} className="animate-pulse">
                          {Array.from({ length: 4 }).map((__, j) => (
                            <td key={j} className="py-3 px-4">
                              <div className="h-4 bg-gray-200 rounded"></div>
                            </td>
                          ))}
                        </tr>
                      ))
                    : tradingLeaderboard?.data?.map((entry) => (
                        <tr
                          key={entry.trader}
                          className="hover:bg-[#E6EBEC] border-b border-b-[#1E1E1E]"
                        >
                          <td className="py-3 px-4">
                            {getRankBadge(entry.rank)}
                          </td>
                          <td className="py-3 px-4">
                            <div
                              onClick={(e) =>
                                handleAddressClick(entry.trader, e)
                              }
                              className="cursor-pointer hover:text-blue-600"
                            >
                              <AddressWithCopy address={entry.trader} />
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex flex-col">
                              <span>
                                ${formatLargeNumber(entry.total_volume.usd)}
                              </span>
                              <span className="text-gray-600 text-sm">
                                {formatLargeNumber(entry.total_volume.mas)} MAS
                              </span>
                            </div>
                          </td>
                          <td className="py-3 px-4">{entry.trade_count}</td>
                        </tr>
                      ))}
                </tbody>
              </table>
            </div>
          ) : (
            /* Liquidity view (pools or users) ------------------------------- */
            renderLiquidityContent()
          )}
        </div>

        {/* ----------------------------- Cards (Mobile) ---------------------------- */}
        <div className="md:hidden space-y-4 mt-4">
          {activeTab === "trading"
            ? /* Trading Cards */
              tradingLeaderboard.isFetching
              ? Array.from({ length: limit }).map((_, index) => (
                  <div
                    key={index}
                    className="bg-[#F9FAFB] rounded-xl p-4 border-2 border-[#1E1E1E] animate-pulse"
                  >
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                ))
              : (tradingLeaderboard.data as TradingLeaderboardEntry[]).map(
                  (entry) => (
                    <div
                      key={entry.trader}
                      className="bg-[#F9FAFB] rounded-xl p-4 border-2 border-[#1E1E1E] hover:bg-[#E6EBEC] transition-colors"
                    >
                      <div className="flex justify-between items-center flex-wrap">
                        <div className="flex items-center gap-3">
                          {getRankBadge(entry.rank)}
                          <AddressWithCopy address={entry.trader} />
                        </div>
                        <div className="text-right">
                          <div className="text-slate-800">
                            {formatLargeNumber(entry.total_volume.mas)} MAS
                          </div>
                          <div className="text-sm text-gray-600">
                            ${formatLargeNumber(entry.total_volume.usd)}
                          </div>
                        </div>
                      </div>
                      <div className="mt-2 text-sm text-gray-600">
                        <div className="flex justify-between">
                          <span>Trades:</span>
                          <span>{entry.trade_count}</span>
                        </div>
                      </div>
                    </div>
                  )
                )
            : selectedPool
            ? /* Pool Contributors Cards */
              liquidityUsers.isFetching
              ? Array.from({ length: limit }).map((_, index) => (
                  <div
                    key={index}
                    className="bg-[#F9FAFB] rounded-xl p-4 border-2 border-[#1E1E1E] animate-pulse"
                  >
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                ))
              : (liquidityUsers.data as PoolUserLeaderboardEntry[]).map(
                  (entry) => (
                    <div
                      key={entry.user_address}
                      className="bg-[#F9FAFB] rounded-xl p-4 border-2 border-[#1E1E1E] hover:bg-[#E6EBEC] transition-colors"
                    >
                      <div className="flex justify-between items-center flex-wrap">
                        <div className="flex items-center gap-3">
                          {getRankBadge(entry.rank)}
                          <div
                            onClick={(e) =>
                              handleAddressClick(entry.user_address, e)
                            }
                            className="cursor-pointer hover:text-blue-600"
                          >
                            <AddressWithCopy address={entry.user_address} />
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-slate-800">
                            {formatLargeNumber(
                              Number(
                                formatUnits(
                                  BigInt(entry.lp_amount),
                                  Math.max(
                                    selectedPool.a_token.decimals,
                                    selectedPool.b_token.decimals
                                  )
                                )
                              )
                            )}{" "}
                            LP
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                )
            : /* Liquidity Pools Cards */
            liquidityPools.isFetching
            ? Array.from({ length: limit }).map((_, index) => (
                <div
                  key={index}
                  className="bg-[#F9FAFB] rounded-xl p-4 border-2 border-[#1E1E1E] animate-pulse"
                >
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              ))
            : (liquidityPools.data as PoolLeaderboardEntry[]).map((entry) => (
                <div
                  key={entry.pool_address}
                  className="bg-[#F9FAFB] rounded-xl p-4 border-2 border-[#1E1E1E] hover:bg-[#E6EBEC] transition-colors"
                >
                  <div className="flex justify-between items-center flex-wrap">
                    <div className="flex items-center gap-3 flex-wrap">
                      {getRankBadge(entry.rank)}
                      {getPoolTokenPair(entry)}
                      <AddressWithCopy address={entry.pool_address} />
                    </div>
                    <div className="text-right">
                      <div className="text-slate-800">
                        ${formatLargeNumber(entry.total_liquidity_usd)}
                      </div>
                    </div>
                  </div>
                  <div className="mt-2 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>Reserves:</span>
                      <span>
                        {formatLargeNumber(entry.a_reserve)}{" "}
                        {entry.a_token.symbol} /{" "}
                        {formatLargeNumber(entry.b_reserve)}{" "}
                        {entry.b_token.symbol}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
        </div>

        {/* ------------------------- Pagination --------------------------- */}
        {(activeTab === "trading"
          ? tradingLeaderboard?.data?.length > 0
          : selectedPool
          ? liquidityUsers?.data?.length > 0
          : liquidityPools?.data?.length > 0) && (
          <div className="flex justify-center items-center gap-4 mt-6">
            {/* Prev ------------------------------------------------------ */}
            <button
              onClick={() => {
                const current =
                  activeTab === "trading"
                    ? tradingLeaderboard
                    : selectedPool
                    ? liquidityUsers
                    : liquidityPools;
                current.setCurrentPage((p) => Math.max(1, p - 1));
              }}
              disabled={
                (activeTab === "trading"
                  ? tradingLeaderboard.currentPage === 1
                  : selectedPool
                  ? liquidityUsers.currentPage === 1
                  : liquidityPools.currentPage === 1) ||
                (activeTab === "trading"
                  ? tradingLeaderboard.isFetching
                  : selectedPool
                  ? liquidityUsers.isFetching
                  : liquidityPools.isFetching)
              }
              className="px-4 py-2 rounded-md border-2 border-[#1E1E1E] bg-white hover:bg-[#E6EBEC] disabled:opacity-50"
            >
              Previous
            </button>

            {/* Page indicator ------------------------------------------- */}
            <span className="text-slate-800">
              Page{" "}
              {activeTab === "trading"
                ? tradingLeaderboard.currentPage
                : selectedPool
                ? liquidityUsers.currentPage
                : liquidityPools.currentPage}
            </span>

            {/* Next ------------------------------------------------------ */}
            <button
              onClick={() => {
                const current =
                  activeTab === "trading"
                    ? tradingLeaderboard
                    : selectedPool
                    ? liquidityUsers
                    : liquidityPools;
                current.setCurrentPage((p) => p + 1);
              }}
              disabled={
                (activeTab === "trading"
                  ? !tradingLeaderboard.hasMore
                  : selectedPool
                  ? !liquidityUsers.hasMore
                  : !liquidityPools.hasMore) ||
                (activeTab === "trading"
                  ? tradingLeaderboard.isFetching
                  : selectedPool
                  ? liquidityUsers.isFetching
                  : liquidityPools.isFetching)
              }
              className="px-4 py-2 rounded-md border-2 border-[#1E1E1E] bg-white hover:bg-[#E6EBEC] disabled:opacity-50"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Leaderboard;
