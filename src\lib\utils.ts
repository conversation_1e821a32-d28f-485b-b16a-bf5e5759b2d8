import { U64 } from "@massalabs/massa-web3";
import { Operation, OperationStatus } from "@massalabs/massa-web3";

// Helper function to format time compactly
export const formatCompactTime = (date: Date) => {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHrs = Math.floor(diffMin / 60);
  const diffDays = Math.floor(diffHrs / 24);

  if (diffSec < 60) return `${diffSec}s`;
  if (diffMin < 60) return `${diffMin}m`;
  if (diffHrs < 24) return `${diffHrs}h`;
  if (diffDays === 1) return "1d";
  return `${diffDays}d`;
};

export async function waitForExecution(
  operation: Operation
): Promise<[number, boolean]> {
  const executionMode = import.meta.env.VITE_EXECUTION_MODE || "speculative";

  if (executionMode === "final") {
    const status = await operation.waitFinalExecution();
    return [status, status === OperationStatus.Success];
  } else {
    const status = await operation.waitSpeculativeExecution();
    return [status, status === OperationStatus.SpeculativeSuccess];
  }
}

export async function getOperationEvents(operation: Operation): Promise<any[]> {
  const executionMode = import.meta.env.VITE_EXECUTION_MODE || "speculative";

  if (executionMode === "final") {
    return await operation.getFinalEvents();
  } else {
    return await operation.getSpeculativeEvents();
  }
}

export function isOperationSuccessful(status: number): boolean {
  const executionMode = import.meta.env.VITE_EXECUTION_MODE || "speculative";

  return executionMode === "final"
    ? status === OperationStatus.Success
    : status === OperationStatus.SpeculativeSuccess;
}

export const shortenAddress = (address: string, chars = 10): string => {
  if (!address) return "";
  return `${address.slice(0, chars + 2)}...${address.slice(-chars)}`;
};

export const validateInput = (value: string) => {
  return /^\d*\.?\d*$/.test(value);
};

export const getTradeTypeInfo = (action: string) => {
  switch (action.toLowerCase()) {
    case "buy":
      return { text: "Buy", bg: "bg-green-100", textColor: "text-green-800" };
    case "sell":
      return { text: "Sell", bg: "bg-red-100", textColor: "text-red-800" };
    case "add_liquidity":
      return {
        text: "Add Liq.",
        bg: "bg-blue-100",
        textColor: "text-blue-800",
      };
    case "remove_liquidity":
      return {
        text: "Remove Liq.",
        bg: "bg-yellow-100",
        textColor: "text-yellow-800",
      };
    case "swap":
      return {
        text: "Swap",
        bg: "bg-purple-100",
        textColor: "text-purple-800",
      };
    default:
      return { text: action, bg: "bg-gray-100", textColor: "text-gray-800" };
  }
};

export const formatBalance = (
  balance: number | undefined,
  decimals: number = 4
) => {
  if (balance === undefined || isNaN(balance)) return "0.0000";
  // We use toFixed (with decimals + 1) to avoid rounding issues and scientific notations of the toString() method
  const parts = balance.toFixed(decimals + 1).split(".");
  if (parts.length === 1) return parts[0];
  return `${parts[0]}.${parts[1].slice(0, decimals)}`;
};

export const formatStringDecimals = (
  value: string,
  decimals: number = 4
): string => {
  const parts = value.split(".");
  if (parts.length === 1) return parts[0];
  return `${parts[0]}.${(parts[1] || "").slice(0, decimals)}`;
};

export const truncateDecimals = (
  value: number | undefined,
  decimals: number = 4
): string => {
  if (value === undefined || isNaN(value)) return "0.0000";
  const str = value.toString();
  const parts = str.split(".");
  if (parts.length === 1) return parts[0];
  return `${parts[0]}.${(parts[1] || "").slice(0, decimals)}`;
};

export function computeMintStorageCost(receiver: string) {
  const STORAGE_BYTE_COST = 100_000;
  const STORAGE_PREFIX_LENGTH = 4;
  const BALANCE_KEY_PREFIX_LENGTH = 7;

  const baseLength = STORAGE_PREFIX_LENGTH;
  const keyLength = BALANCE_KEY_PREFIX_LENGTH + receiver.length;
  const valueLength = 4 * U64.SIZE_BYTE;

  return (baseLength + keyLength + valueLength) * STORAGE_BYTE_COST;
}

// Native MAS coin address to determine if the token address is the native Mas coin
export const NATIVE_MAS_COIN_ADDRESS = "NATIVE_COIN";

export const WMAS_TOKEN_ADDRESS =
  import.meta.env.VITE_AI_NETWORK.toUpperCase() === "MAINNET"
    ? "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9"
    : "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU";

export const MASSA_ROLLS_ADDRESS = "MASSA_ROLLS";

export const MODEL_NAME = "gemini-2.0-flash-exp";
